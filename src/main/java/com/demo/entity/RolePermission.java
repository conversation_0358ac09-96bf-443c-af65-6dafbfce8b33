package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 角色权限表
 */
@Data
@TableName(value = "role_permission")
public class RolePermission {
    /**
     * id
     */
    @TableId(value = "rp_id", type = IdType.AUTO)
    private Integer rpId;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    private Integer roleId;

    /**
     * 权限id
     */
    @TableField(value = "permission_id")
    private Integer permissionId;
}