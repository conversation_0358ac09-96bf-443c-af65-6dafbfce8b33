package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 请假记录表
 */
@Data
@TableName(value = "`leave`")
public class Leave {
    /**
     * 请假记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 员工ID
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 员工姓名
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 请假类型（1-病假，2-事假，3-年假，4-调休，5-婚假，6-产假，7-丧假）
     */
    @TableField(value = "leave_type")
    private Integer leaveType;

    /**
     * 请假开始时间
     */
    @TableField(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 请假结束时间
     */
    @TableField(value = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 请假原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 请假状态（0-待审批，1-已批准，2-已拒绝，3-已取消）
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 审批人ID
     */
    @TableField(value = "approved_by")
    private Integer approvedBy;

    /**
     * 审批时间
     */
    @TableField(value = "approved_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvedTime;

    /**
     * 审批意见
     */
    @TableField(value = "approval_comment")
    private String approvalComment;

    /**
     * 取消原因
     */
    @TableField(value = "cancel_reason")
    private String cancelReason;

    /**
     * 取消时间
     */
    @TableField(value = "cancel_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 市
     */
    @TableField(value = "`city`")
    private String city;

    /**
     * 县（区）
     */
    @TableField(value = "`county`")
    private String county;

    /**
     * 乡（镇）
     */
    @TableField(value = "`township`")
    private String township;

    /**
     * 村
     */
    @TableField(value = "`hamlet`")
    private String hamlet;

    /**
     * 点
     */
    @TableField(value = "`site`")
    private String site;
}