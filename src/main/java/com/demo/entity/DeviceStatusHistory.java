package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 设备状态历史记录表
 */
@Data
@TableName(value = "device_status_history")
public class DeviceStatusHistory {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备编号
     */
    @TableField(value = "equipment_number")
    private String equipmentNumber;

    /**
     * 设备类型
     */
    @TableField(value = "device_type")
    private String deviceType;

    /**
     * 设备IP
     */
    @TableField(value = "ip")
    private String ip;

    /**
     * 状态开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 状态结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 状态（0-在线，1-离线）
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 状态变化原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 持续时间（秒）
     */
    @TableField(value = "duration")
    private Long duration;
}