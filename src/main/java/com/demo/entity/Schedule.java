package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 排班实体类，表示员工的具体排班信息。
 */
@Data
@TableName("schedule")
public class Schedule {
    @TableId(type = IdType.AUTO)
    private Integer id;  // 主键ID
    private Integer userId;         // 员工ID
    private Integer shiftId;        // 班次ID
    private String userName;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime scheduleDate;  // 排班日期
    private Integer scheduleType;   // 排班类型：1-常规排班，2-临时排班
    private String remark;          // 备注说明
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime; // 创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime; // 更新时间
    
    // 地区字段
    @TableField(exist = false)
    private String city;            // 市
    @TableField(exist = false)
    private String county;          // 县（区）
    @TableField(exist = false)
    private String township;        // 乡村（镇）
    @TableField(exist = false)
    private String hamlet;          // 村
    @TableField(exist = false)
    private String site;            // 点位
}