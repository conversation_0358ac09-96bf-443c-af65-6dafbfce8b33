package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 报警记录表
 */
@Data
@TableName(value = "`alarm`")
public class Alarm {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 报警类型
     */
    @TableField(value = "`wf_uuid`")
    private String wfUuid;


    /**
     * 报警类型
     */
    @TableField(value = "`alarm_type`")
    private String alarmType;

    /**
     * 报警时间
     */
    @TableField(value = "`event_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventTime;

    /**
     * 报警内容
     */
    @TableField(value = "`content`")
    private String content;

    /**
     * 程度(一般，轻微，严重)
     */
    @TableField(value = "`degree`")
    private String degree;

    /**
     * 报警对象类型
     */
    @TableField(value = "`target_type`")
    private String targetType;

    /**
     * 0已确定，1未确定
     */
    @TableField(value = "`know`")
    private Integer know;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 市
     */
    @TableField(value = "`city`")
    private String city;

    /**
     * 区县
     */
    @TableField(value = "`county`")
    private String county;

    /**
     * 乡镇
     */
    @TableField(value = "`township`")
    private String township;

    /**
     * 村
     */
    @TableField(value = "`hamlet`")
    private String hamlet;

    /**
     * 点位
     */
    @TableField(value = "`site`")
    private String site;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}