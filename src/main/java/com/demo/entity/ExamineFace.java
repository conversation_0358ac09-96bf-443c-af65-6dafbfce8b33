package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 审核人脸
 */
@Data
@TableName(value = "`examine_face`")
public class ExamineFace {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 上传设备编号
     */
    @TableField(value = "`equipmentNumber`")
    private String equipmentnumber;

    /**
     * 人脸图片
     */
    @TableField(value = "`examine_url`")
    private String examineUrl;

    /**
     * 审核状态
     */
    @TableField(value = "`examine`")
    private String examine;

    /**
     * 绑定人员ID
     */
    @TableField(value = "`binding_userId`")
    private Integer bindingUserid;

    /**
     * 绑定人员姓名
     */
    @TableField(value = "`binding_userName`")
    private String bindingUsername;

    /**
     * 审核人ID
     */
    @TableField(value = "`reviewer_id`")
    private Integer reviewerId;

    /**
     * 审核人
     */
    @TableField(value = "`reviewer`")
    private String reviewer;

    /**
     * 市
     */
    @TableField(value = "`city`")
    private String city;

    /**
     * 所在县（区）
     */
    @TableField(value = "`county`")
    private String county;

    /**
     * 所在乡（镇）
     */
    @TableField(value = "`township`")
    private String township;

    /**
     * 所在村
     */
    @TableField(value = "`hamlet`")
    private String hamlet;

    /**
     * 所在点位
     */
    @TableField(value = "`site`")
    private String site;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 审核时间
     */
    @TableField(value = "`reviewer_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewerTime;
}