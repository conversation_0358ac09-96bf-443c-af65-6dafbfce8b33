package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 班次组关系实体类
 * 用于表示班次与班次组之间的多对多关系
 */
@Data
@TableName("shift_group_relation")
public class ShiftGroupRelation {
    @TableId(type = IdType.AUTO)
    private Integer id;  // 主键ID

    private Integer shiftId;  // 班次ID

    private Integer groupId;  // 班次组ID
} 