package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 精准劝导表
 */
@Data
@TableName(value = "`accurate_persuasion`")
public class AccuratePersuasion {
    /**
     * UUID
     */
    @TableId(value = "UUID", type = IdType.INPUT)
    private String uuid;

    /**
     * 违法记录id
     */
    @TableField(value = "`illegal_records_uuid`")
    private String illegalRecordsUuid;

    /**
     * 违法名称
     */
    @TableField(value = "`Illegal_name`")
    private String illegalName;

    /**
     * 预处理人员
     */
    @TableField(value = "`user_id`")
    private Integer userId;

    /**
     * 预处理人员姓名
     */
    @TableField(value = "`user_name`")
    private String userName;

    /**
     * 处理方式(打电话还是上门)
     */
    @TableField(value = "`disposal_method`")
    private String disposalMethod;

    /**
     * 处理状态(0未处理，1已处理)
     */
    @TableField(value = "`disposal_status`")
    private Integer disposalStatus;

    /**
     * 拍照图片地址
     */
    @TableField(value = "`imgl`")
    private String imgl;

    /**
     * 实际处理人员
     */
    @TableField(value = "`actual_processing`")
    private Integer actualProcessing;

    /**
     * 实际处理人员姓名
     */
    @TableField(value = "`actual_processing_name`")
    private String actualProcessingName;

    /**
     * 备注
     */
    @TableField(value = "`remarks`")
    private String remarks;

    /**
     * 处理时间
     */
    @TableField(value = "`processing_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss", timezone = "GMT+8")
    private Date processingTime;

    /**
     * 截止处理时间
     */
    @TableField(value = "`deadline_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss", timezone = "GMT+8")
    private Date deadlineTime;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 市
     */
    @TableField(value = "`city`")
    private String city;

    /**
     * 县（区）
     */
    @TableField(value = "`county`")
    private String county;

    /**
     * 乡村（镇）
     */
    @TableField(value = "`township`")
    private String township;

    /**
     * 村
     */
    @TableField(value = "`hamlet`")
    private String hamlet;

    /**
     * 点位
     */
    @TableField(value = "`site`")
    private String site;

    /**
     * 违法人员姓名
     */
    @TableField(value = "`illegal_personnel_name`")
    private String illegalPersonnelName;

    /**
     * 违法人员身份证
     */
    @TableField(value = "`illegal_personnel_id_card`")
    private String illegalPersonnelIdCard;

    /**
     * 违法人员电话
     */
    @TableField(value = "`illegal_personnel_phone`")
    private String illegalPersonnelPhone;

    /**
     * 违法人员性别
     */
    @TableField(value = "`illegal_personnel_gender`")
    private String illegalPersonnelGender;

    /**
     * 下派类型
     */
    @TableField(value = "`lower_faction_type`")
    private String lowerFactionType;
}