package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demo.enums.AttendanceEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考勤实体类，记录员工的考勤信息。
 */
@Data
@TableName("attendance")
public class Attendance {
    @TableId(type = IdType.AUTO)
    private Integer id;  // 主键ID
    private Integer userId;      // 用户ID，对应Users表的user_id
    private Integer scheduleId;  // 对应的排班ID
    private String userName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkInTime;    // 签到时间
    private Integer type; //打卡类型
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkOutTime;   // 签退时间
    private AttendanceEnum status;      // 考勤状态：0-正常，1-迟到，2-早退，3-缺勤，4-请假
    private String remark;       // 备注说明
    private String imageUrl;    // 打卡图片地址
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;   // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;   // 修改时间
} 