package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 全局配置
 */
@Data
@TableName(value = "`relatedconfigurations`")
public class Relatedconfigurations {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 超载人数超过多少下派（人）
     */
    @TableField(value = "`overload`")
    private Integer overload;

    /**
     * 超载日志多久删除一次（天）
     */
    @TableField(value = "`log_save_time`")
    private Integer logSaveTime;

    /**
     * 劝导员离岗时间超过多久为脱岗(分钟)
     */
    @TableField(value = "`off_duty_time`")
    private Integer offDutyTime;

    /**
     * 设备多久没有上报状态，认为设备离线（分钟）
     */
    @TableField(value = "`equipment_offline_time`")
    private Integer equipmentOfflineTime;

    /**
     * 是否启动自动打电话功能
     */
    @TableField(value = "`phone`")
    private Boolean phone;

    /**
     * 上次视频时间多少秒认为是劝导有效（秒）
     */
    @TableField(value = "`effective_persuasion`")
    private Integer effectivePersuasion;
}