package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "`tree_node`")
public class TreeNode implements Serializable {
    /**
     * 节点ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父节点ID（顶级节点为0）
     */
    @TableField(value = "`parentId`")
    private Integer parentId;

    /**
     * 节点名称
     */
    @TableField(value = "`label`")
    private String label;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 节点级别
     */
    //@TableField(exist = false)  //表示这个属性不属于表中的字段
    @TableField(value = "`level`")
    private int level; // 添加级别字段

    /**
     * 子节点
     */
    @TableField(exist = false)  //表示这个属性不属于表中的字段
    private List<TreeNode> childList;


}