package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 人脸图片地址表
 */
@Data
@TableName(value = "`face`")
public class Face {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 人脸图片url
     */
    @TableField(value = "`face_url`")
    private String faceUrl;

    /**
     * 对应人员ID
     */
    @TableField(value = "`user_id`")
    private Integer userId;

    /**
     * 人员姓名
     */
    @TableField(value = "`user_name`")
    private String userName;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`")
    private Date createTime;
}