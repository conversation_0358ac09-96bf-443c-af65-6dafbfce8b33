package com.demo.entity.DTO;

import lombok.Data;
import java.util.List;
import java.util.ArrayList;

/**
 * 设备树节点DTO
 * 用于构建设备的树形结构，支持地理位置层级和设备信息
 * 
 * <AUTHOR>
 */
@Data
public class DeviceTreeNode {

    /**
     * 节点名称/标签（地理位置使用地名，设备使用设备名称）
     */
    private String label;

    /**
     * 节点类型：location（地理位置）、device（设备）
     */
    private String type;

    /**
     * 地理位置层级：city、county、township、hamlet、site
     * 仅当type为location时有效
     */
    private String level;

    /**
     * 子节点列表
     */
    private List<DeviceTreeNode> children;

    /**
     * 设备相关信息（仅当type为device时有效）
     */
    private DeviceInfo deviceInfo;
    
    /**
     * 设备信息内部类
     */
    @Data
    public static class DeviceInfo {
        /**
         * 设备ID
         */
        private Integer deviceId;
        
        /**
         * 设备名称
         */
        private String deviceName;
        
        /**
         * 设备编号
         */
        private String equipmentNumber;
        
        /**
         * 设备状态（0正常1不正常）
         */
        private Integer state;
        
        /**
         * 设备IP
         */
        private String ip;

        /**
         * 流密钥
         */
        private String streamKey;
    }
    

    
    /**
     * 构造函数 - 创建地理位置节点
     */
    public DeviceTreeNode(String label, String level) {
        this.label = label;
        this.type = "location";
        this.level = level;
        this.children = new ArrayList<>();
    }

    /**
     * 构造函数 - 创建设备节点
     */
    public DeviceTreeNode(String label, DeviceInfo deviceInfo) {
        this.label = label;
        this.type = "device";
        this.deviceInfo = deviceInfo;
        this.children = new ArrayList<>();
    }
    
    /**
     * 添加子节点
     */
    public void addChild(DeviceTreeNode child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
    }
    

}
