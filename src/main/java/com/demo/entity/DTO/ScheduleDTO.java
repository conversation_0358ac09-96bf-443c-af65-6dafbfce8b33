package com.demo.entity.DTO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ScheduleDTO {
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;


    /**
     * 乡
     */
    private String township;
    /**
     * 所在村
     */
    private String hamlet;

    /**
     * 点
     */
    private String site;

    /**
     * 上班人姓名
     */
    private String userName;

    /**
     * 上班人ID
     */
    @TableField(value = "`user_id`")
    private Integer userId;
}
