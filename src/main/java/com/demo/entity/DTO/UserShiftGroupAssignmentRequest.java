package com.demo.entity.DTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
@Data
public class UserShiftGroupAssignmentRequest {
    private Integer userId;        // 用户ID
    private Integer groupId;       // 班次组ID
    private Integer fixedShiftId;  // 固定班次ID（如果是轮班制则为null）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate; // 生效日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;  //结束日期

} 