package com.demo.entity.DTO;

import com.demo.enums.PersuasionEnum;
import com.demo.enums.PlateColorEnum;
import com.demo.enums.TrafficEnum;
import com.demo.enums.VehicleTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class IllegalRecordsDTO {
    /**
     * 违法类型（0正常车辆 1未佩戴头盔 2加装遮阳伞 3超员）
     */
    private TrafficEnum illegalType;

    /**
     * 车辆类型（0非机动车，1三轮车）
     */
    private VehicleTypeEnum vehicleType;
    /**
     * 车牌颜色（0黄色，1白色，2绿色，3蓝色，4红色）
     */
    private PlateColorEnum plateColor;
    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 乘坐人数
     */
    private Integer numberOfPassengers;

    /**
     * 是否进行劝导动作(0正常车辆不进行劝导，1已进行劝导，2未进行劝导)
     */
    private PersuasionEnum persuasiveBehavior;

    /**
     * 必须传（凭证） 9999
     */
    private String token;

    /**
     * 设备编号
     */
    private String equipmentNumber;
    /**
     * 抓拍时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date captureTime;

    /**
     * 路口设备重启时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date restartTime;

    /**
     * 路口定义车辆ID
     */
    private Integer vehicleId;

    /**
     * 路口摄像头名称
     */
    private String cameraName;

    /**
     * 路口定义车辆IDTow
     */
    private Integer vehicleIdTwo;

    /**
     * 路口摄像头名称Tow
     */
    private String cameraNameTwo;

    private Boolean isComing1;
    private Boolean isComing2;
    /**
     * 在来向画面中打印的提示文本
     */
    private String tipsText;
    /**
     * 左边画面的车辆与乘客合并区域（缩放后）
     */
    private Integer[] illegalZone1;
    /**
     * 右边画面的车辆与乘客合并区域（缩放后)
     */
    private Integer[] illegalZone2;

}
