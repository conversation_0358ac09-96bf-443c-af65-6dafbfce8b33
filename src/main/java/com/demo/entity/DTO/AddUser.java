package com.demo.entity.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 增加用户实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AddUser {

    /**
     * 电话
     */
    private String phone;
    /**
     * 密码
     */
    private String password;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;


    /**
     * 乡
     */
    private String township;
    /**
     * 所在村
     */
    private String hamlet;
    /**
     * 点
     */
    private String site;

    /**
     * 用户角色
     */
    @NotNull
    private List<Integer> role;
}
