package com.demo.entity.DTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 违法处理的下派信息详细
 */
@Data
public class PersuasionInDetail {
    /**
     * 预处理人员
     */
    private List<String> userNames;
    /**
     * 实际处理人员
     */
    private String name;
    /**
     * 处理方式(打电话还是上门)
     */
    private String disposalMethod;
    /**
     * 处理状态(0未处理，1已处理)
     */
    private Integer disposalStatus;

    /**
     * 拍照图片地址
     */
    private String imgl;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 处理时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date processingTime;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
