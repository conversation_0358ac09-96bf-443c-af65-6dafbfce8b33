package com.demo.entity.DTO;

import com.demo.entity.Shift;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
@Data
public class ShiftGroupDTO {
    private Integer id; //ID
    private String groupName;//班组每次
    private String workPattern; //上班周期
    private List<Shift> shifts; //班次列表
    private String description;  // 描述信息
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime; // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime; // 更新时间

}
