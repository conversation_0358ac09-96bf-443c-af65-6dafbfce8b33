package com.demo.entity.VO;

import lombok.Data;

/**
 * 考勤统计信息
 */
@Data
public class AttendanceStats {
    private Integer totalDays;      // 应出勤天数
    private Integer actualDays;     // 实际出勤天数
    private Integer lateDays;       // 迟到次数
    private Integer earlyDays;      // 早退次数
    private Integer absentDays;     // 缺勤天数
    private Integer leaveDays;      // 请假天数
    
    // 可以根据需要添加其他统计信息
    private Double attendanceRate;  // 出勤率
    private Double onTimeRate;      // 准时率
} 