package com.demo.entity.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
public class ScheduleVO {
    private Integer id;
    private Integer userId;
    private String userName;    // 用户姓名，对应Users表的name
    private String deptName;    // 部门名称
    private Integer shiftId;
    private String shiftName;   // 班次名称
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime scheduleDate;
    private LocalTime startTime;    // 上班时间
    private LocalTime endTime;      // 下班时间
    private String workStatus;      // 工作状态：工作日/休息日
    private AttendanceVO attendance; // 考勤信息
} 