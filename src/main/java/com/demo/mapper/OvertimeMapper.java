package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.Overtime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface OvertimeMapper extends BaseMapper<Overtime> {
    /**
     * 获取用户当天的加班记录
     *
     * @param userId 用户ID
     * @param date   日期
     * @return 加班记录列表
     */
    List<Overtime> getUserOvertimeByDate(@Param("userId") Integer userId, @Param("date") LocalDate date);

    /**
     * 获取指定时间范围内的加班记录
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 加班记录列表
     */
//    @Select("SELECT * FROM overtime WHERE user_id = #{userId} AND start_time >= #{startTime} AND start_time < #{endTime} ORDER BY start_time DESC")
    List<Overtime> getUserOvertimeByTimeRange(@Param("userId") Integer userId,
                                              @Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 获取指定日期范围内的所有加班记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 加班记录列表
     */
//    @Select("SELECT * FROM overtime WHERE DATE(start_time) >= #{startDate} AND DATE(start_time) <= #{endDate} ORDER BY start_time DESC")
    List<Overtime> getAllOvertimesByDateRange(@Param("startDate") LocalDate startDate,
                                              @Param("endDate") LocalDate endDate);

    /**
     * 获取指定区域和日期范围内的加班记录
     *
     * @param city      市
     * @param county    县
     * @param township  镇
     * @param hamlet    村
     * @param site      点位
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 加班记录列表
     */

    List<Overtime> getOvertimesByAreaAndDateRange(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
}