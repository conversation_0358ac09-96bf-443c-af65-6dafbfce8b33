package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.RoleUsers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RoleUsersMapper extends BaseMapper<RoleUsers> {
    void insertRole(@Param("insertedUserId") Integer insertedUserId, @Param("role") List<Integer> role);
}