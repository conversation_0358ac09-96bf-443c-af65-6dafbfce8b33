package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.Alarm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AlarmMapper extends BaseMapper<Alarm> {
    @Select("SELECT * FROM alarm ORDER BY create_time DESC LIMIT #{limit}")
    List<Alarm> selectLatestAlarms(@Param("limit") int limit);
}