package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.IllegalRecords;
import com.demo.entity.ViolationCount;
import com.demo.entity.ViolationTrend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface IllegalRecordsMapper extends BaseMapper<IllegalRecords> {

    // 查询年内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByYear(@Param("year") int year);

    // 查询月内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByMonth(@Param("year") int year, @Param("month") int month);

    // 查询日内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByDay(@Param("year") int year, @Param("month") int month, @Param("day") int day);

    // 查询周内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByWeek(@Param("year") int year, @Param("week") int week);

    // 查询过去七天内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByLastSevenDays();

    // 获取按年统计的违法类型分布趋势
    List<ViolationTrend> selectViolationTrendsByYear(@Param("year") int year, @Param("city") String city, @Param("county") String county,
                                                     @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    // 获取按月统计的违法类型分布趋势
    List<ViolationTrend> selectViolationTrendsByMonth(@Param("year") int year, @Param("month") int month, @Param("city") String city,
                                                      @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    // 获取按周统计的违法类型分布趋势
    List<ViolationTrend> selectViolationTrendsByWeek(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("city") String city,
                                                     @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    // 获取按天统计的违法类型次数
    List<ViolationCount> selectViolationCountsByDay(@Param("date") LocalDate date, @Param("city") String city, @Param("county") String county,
                                                    @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    // 获取按天统计的违法类型分布趋势
    List<ViolationTrend> selectViolationTrendsByDay(@Param("date") LocalDate date, @Param("city") String city, @Param("county") String county,
                                                    @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    List<ViolationCount> selectViolationCountsByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("city") String city,
                                                          @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    @Select("SELECT DATE(create_time) as date, COUNT(*) as count FROM illegal_records WHERE DATE(create_time) BETWEEN #{startDate} AND #{endDate} GROUP BY DATE(create_time)")
    List<Map<String, Object>> selectIllegalCountsByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    @Select("SELECT DATE(create_time) as date, COUNT(*) as count FROM illegal_records WHERE disposal_status != 0 AND DATE(create_time) BETWEEN #{startDate} AND #{endDate} GROUP BY DATE(create_time)")
    List<Map<String, Object>> selectDispatchedCountsByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    int getTotalCountForToday(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                              @Param("hamlet") String hamlet, @Param("site") String site, @Param("date") Date date);

    int getHandledCountForToday(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                                @Param("hamlet") String hamlet, @Param("site") String site, Date date);

    @Select("SELECT COUNT(*) FROM illegal_records WHERE DATE(create_time) = CURDATE() AND disposal_status IN (1, 3)")
    int getPendingCountForToday();

    @Select("SELECT COUNT(*) FROM illegal_records WHERE YEARWEEK(create_time, 1) = YEARWEEK(CURDATE(), 1)")
    int getTotalCountForWeek();

    @Select("SELECT COUNT(*) FROM illegal_records WHERE YEARWEEK(create_time, 1) = YEARWEEK(CURDATE(), 1) AND disposal_status != 0")
    int getHandledCountForWeek();

    @Select("SELECT COUNT(*) FROM illegal_records WHERE YEARWEEK(create_time, 1) = YEARWEEK(CURDATE(), 1) AND disposal_status IN (1, 3)")
    int getPendingCountForWeek();

    int getTotalCountForMonth(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                              @Param("hamlet") String hamlet, @Param("site") String site, @Param("date") Date date);

    int getHandledCountForMonth(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                                @Param("hamlet") String hamlet, @Param("site") String site, @Param("date") Date date);

    List<Map<String, Integer>> getViolationTypeCountsForToday(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                                                              @Param("hamlet") String hamlet, @Param("site") String site);

    List<Map<String, Object>> topLocationsByDay(@Param("year") int year, @Param("month") int month, @Param("day") int day,
                                                @Param("city") String city, @Param("county") String county, @Param("township") String township,
                                                @Param("hamlet") String hamlet, @Param("site") String site,@Param("groupByLevel") String groupByLevel);


    List<Map<String, Object>> topLocationsByMonth(@Param("year") int year, @Param("month") int month, @Param("city") String city,
                                                  @Param("county") String county, @Param("township") String township,
                                                  @Param("hamlet") String hamlet, @Param("site") String site,@Param("groupByLevel") String groupByLevel);

    List<Map<String, Object>> selectDayStatistics(@Param("date") LocalDate date);

    List<Map<String, Object>> selectMonthStatistics();

    List<ViolationCount> getTheSameDayViolationType();

    List<ViolationCount> getTheViolationTypeOfTheMonth();

    /**
     * 获取指定小时的违法详情
     */
    @Select("SELECT " +
            "illegal_type as type, " +
            "COUNT(*) as count " +
            "FROM illegal_records " +
            "WHERE DATE(create_time) = #{date} " +
            "AND HOUR(create_time) = #{hour} " +
            "GROUP BY illegal_type")
    List<Map<String, Object>> getViolationDetailsByHour(@Param("date") LocalDate date, @Param("hour") String hour);

    /**
     * 获取当天每小时的违法统计和详情
     */
    List<Map<String, Object>> getDayStatisticsWithDetails(@Param("date") Date date, @Param("city") String city, @Param("county") String county,
                                                          @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    /**
     * 获取当月每天的违法统计和详情
     */
    List<Map<String, Object>> selectMonthStatisticsWithDetails(@Param("date") Date date, @Param("city") String city, @Param("county") String county,
                                                               @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    List<Map<String, Object>> getPointDetailDataByDay(@Param("year") int year, @Param("month") int month, @Param("day") int day,
                                                      @Param("city") String city, @Param("county") String county,
                                                      @Param("township") String township, @Param("hamlet") String hamlet,
                                                      @Param("site") String site);

    List<Map<String, Object>> getPointDetailDataByMonth(@Param("year") int year, @Param("month") int month, @Param("city") String city,
                                                        @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    List<Map<String, Object>> getPointDetailDataByYear(@Param("year") int year, @Param("city") String city, @Param("county") String county,
                                                       @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    /**
     * 获取当天各违法类型的统计数据
     */
    List<Map<String, Object>> getViolationTypeStatisticsToday(@Param("city") String city, @Param("county") String county,
                                                              @Param("township") String township, @Param("hamlet") String hamlet,
                                                              @Param("site") String site,@Param("date") Date date);

    /**
     * 获取工作时间内劝导统计(按地理层级汇总)
     */
    List<Map<String, Object>> getPersuasionStatsByWorkTime(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site
    );

    List<Map<String, Object>> getPersuasionStatsInWorkingHours(
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("workingHours") List<Map<String, Object>> workingHours,
            @Param("theNextLevel") String theNextLevel);

    List<Map<String, Object>> getProselytizerShiftInfo(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    List<Map<String, Object>> getViolationSites(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site
    );

    List<Map<String, Object>> getSiteSchedule(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("date") Date date
    );

    /**
     * 获取指定时间范围内的所有违法地点
     */
    List<Map<String, Object>> getViolationLocations(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site);

    /**
     * 获取指定时间和地点的违法统计（只统计排班时间内的数据）
     */
    List<Map<String, Object>> getViolationStats(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site
    );

    /**
     * 获取指定时间和地点的违法统计
     */
    List<Map<String, Object>> getViolationStats(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("shiftStart") String shiftStart,
            @Param("shiftEnd") String shiftEnd);

    /**
     * 统计指定车牌号在指定时间范围内的违法次数
     * @param plateNumber 车牌号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 违法次数
     */
    int countViolationsByPlateNumber(@Param("plateNumber") String plateNumber,
                                   @Param("startTime") Date startTime,
                                   @Param("endTime") Date endTime);

    /**
     * 获取指定日期的排班时间
     */
    List<Map<String, Object>> getSiteSchedules(
            @Param("date") Date date,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site
    );

    /**
     * 获取点位违法趋势统计(按天统计)
     */
    List<Map<String, Object>> getViolationTrendStats(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site
    );

    /**
     * 获取点位违法趋势统计(按年统计，返回12个月数据)
     */
    List<Map<String, Object>> getLocationTrendStatsByYear(
            @Param("year") int year,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site
    );

    /**
     * 获取24小时违法趋势统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param city      城市
     * @param county    区县
     * @param township  镇街道
     * @param hamlet    社区/村
     * @param site      点位
     * @return 24小时违法趋势统计数据
     */
    List<Map<String, Object>> getHourlyViolationStats(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site
    );

    /**
     * 获取本月违法类型统计数据
     *
     * @return 违法类型统计列表，包含type(违法类型)和count(数量)
     */
    List<Map<String, Object>> getViolationTypeStatisticsByMonth(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                                                                @Param("hamlet") String hamlet, @Param("site") String site,@Param("date") Date date);

    List<Map<String, Object>> getViolationTypeAnalysisByWeek(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                                                             @Param("hamlet") String hamlet, @Param("site") String site);

    List<Map<String, Object>> getViolationTypeAnalysisByYear(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                                                             @Param("hamlet") String hamlet, @Param("site") String site, @Param("year") String year);

    List<Map<String, Object>> getViolationTypeStatisticsByMap(@Param("city") String city, @Param("county") String county, @Param("township") String township,
                                                              @Param("hamlet") String hamlet, @Param("site") String site, @Param("startDate") Date startDate,
                                                              @Param("endDate") Date endDate);

    Map<String, Object> exampleQueryWorkingHoursStatistics(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("city") String city,
                                                                 @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet,
                                                                 @Param("site") String site, @Param("theNextLevel")  String theNextLevel);

    Map<String, Object> getPersuadedCountByWorkingHours(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("city") String city,
                                                              @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet,
                                                              @Param("site") String site, @Param("theNextLevel")  String theNextLevel);

}