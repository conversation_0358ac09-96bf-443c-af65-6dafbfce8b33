package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demo.entity.DTO.IocationDTO;
import com.demo.entity.Users;
import com.demo.entity.VO.AllThePersuadersVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Mapper
public interface UsersMapper extends BaseMapper<Users> {
    List<String> selectPermissionList(int id);

    List<String> selectroleList(int id);

    void updateBatchByIds(List<Integer> ids);

    void updatefrozenUserList(List<Integer> ids);

    List<Users> selectUsers(@Param("offset") int offset, @Param("pageSize") Integer pageSize, @Param("name") String name, @Param("phone") String phone, @Param("sex") String sex, @Param("city") String city, @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site, @Param("state") Integer state);

    long countUsers(@Param("name") String name, @Param("phone") String phone, @Param("sex") String sex, @Param("city") String city, @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site, @Param("state") Integer state);

    List<Users> selectProselytizer(@Param("city") String city, @Param("county") String county, @Param("township") String township, @Param("hamlet") String hamlet, @Param("site") String site);

    IPage<Users> selectProselytizerList(IPage<Users> page, String city, String county, String township, String hamlet, String site);

    IPage<Map<String, Object>> selectProselytizerListMap(IPage<Users> page, String city, String county, String township, String hamlet, String site);

    IPage<AllThePersuadersVO> getAllThePersuaders(@Param("page") IPage<AllThePersuadersVO> page, @Param("iocationDTO") IocationDTO iocationDTO);

    /**
     * 获取点位人员考勤情况
     */
    List<Map<String, Object>> getAttendanceUser(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("date") LocalDate date
    );

    /**
     * 查询劝导员列表
     */
    @Select("SELECT " +
            "u.id as userId, " +
            "u.name, " +
            "u.phone, " +
            "u.dept_name as deptName, " +
            "u.state as status, " +
            "s.id as scheduleId, " +
            "s.shift_name as shiftName, " +
            "s.start_time as startTime, " +
            "s.end_time as endTime " +
            "FROM users u " +
            "LEFT JOIN schedule s ON u.id = s.user_id " +
            "WHERE u.city = #{city} " +
            "AND u.county = #{county} " +
            "AND u.township = #{township} " +
            "AND u.hamlet = #{hamlet} " +
            "AND u.site = #{site} " +
            "AND u.state = 0")
    List<Map<String, Object>> getPersuaders(@Param("city") String city,
                                            @Param("county") String county,
                                            @Param("township") String township,
                                            @Param("hamlet") String hamlet,
                                            @Param("site") String site);

    /**
     * 查询排班记录
     */
    List<Map<String, Object>> getSchedules(
            @Param("city") String city,
            @Param("county") String county,
            @Param("township") String township,
            @Param("hamlet") String hamlet,
            @Param("site") String site,
            @Param("date") LocalDate date
    );
}