package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demo.entity.AccuratePersuasion;
import com.demo.entity.VO.AccuratePersuasionVO;
import com.demo.enums.TrafficEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface AccuratePersuasionMapper extends BaseMapper<AccuratePersuasion> {
    IPage<AccuratePersuasionVO> selectAccuratePersuasionList(@Param("page") IPage<AccuratePersuasionVO> page,
                                                             @Param("illegalType") TrafficEnum illegalType,
                                                             @Param("userName") String userName,
                                                             @Param("disposalMethod") String disposalMethod,
                                                             @Param("disposalStatus") Integer disposalStatus,
                                                             @Param("processingStartTime") Date processingStartTime,
                                                             @Param("processingEndTime") Date processingEndTime,
                                                             @Param("startTime") Date startTime,
                                                             @Param("endTime") Date endTime);

    // 查询总数量
    int countTotal(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    // 查询已处理数量
    int countProcessed(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    // 查询年内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByYear(@Param("year") int year);

    // 查询月内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByMonth(@Param("year") int year, @Param("month") int month);

    // 查询日内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByDay(@Param("year") int year, @Param("month") int month,
                                                      @Param("day") int day);

    // 查询周内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByWeek(@Param("year") int year, @Param("week") int week);

    // 查询过去七天内违法点位最多的前七个
    List<Map<String, Object>> selectTopLocationsByLastSevenDays();

    // 获取按周统计的违法类型分布趋势
    List<Map<String, Object>> selectViolationTrendsByWeek(@Param("year") int year, @Param("week") int week);

    // 获取按月统计的违法类型分布趋势
    List<Map<String, Object>> selectViolationTrendsByMonth(@Param("year") int year, @Param("month") int month);

    // 获取按年统计的违法类型分布趋势
    List<Map<String, Object>> selectViolationTrendsByYear(@Param("year") int year);

    List<Map<String, Object>> getProcessedCount(@Param("city") String city, @Param("county") String county,
                                                @Param("township") String township, @Param("hamlet") String hamlet,
                                                @Param("site") String site);

    List<Map<String, Object>> getUntreatedCount(@Param("city") String city, @Param("county") String county,
                                                @Param("township") String township, @Param("hamlet") String hamlet,
                                                @Param("site") String site);

    List<Map<String, Object>> getProcessedCountByMonth(@Param("city") String city, @Param("county") String county,
                                                       @Param("township") String township,
                                                       @Param("hamlet") String hamlet, @Param("site") String site);

    List<Map<String, Object>> getUntreatedCountByMonth(@Param("city") String city, @Param("county") String county,
                                                       @Param("township") String township,
                                                       @Param("hamlet") String hamlet, @Param("site") String site);

    Map<String, Object> theProcessingRatesOfTheWeekBeforeLast(@Param("city") String city,
                                                              @Param("county") String county,
                                                              @Param("township") String township,
                                                              @Param("hamlet") String hamlet,
                                                              @Param("site") String site);

    Map<String, Object> theProcessingRateOfThePreviousMonth(@Param("city") String city,
                                                            @Param("county") String county,
                                                            @Param("township") String township,
                                                            @Param("hamlet") String hamlet,
                                                            @Param("site") String site);

    List<Map<String, Object>> handleTheChartsOnScheduleOfTheWeek(@Param("city") String city,
                                                                 @Param("county") String county,
                                                                 @Param("township") String township,
                                                                 @Param("hamlet") String hamlet,
                                                                 @Param("site") String site);

    List<Map<String, Object>> handleTheChartsOnScheduleOfThePreviousMonth(@Param("city") String city,
                                                                          @Param("county") String county,
                                                                          @Param("township") String township,
                                                                          @Param("hamlet") String hamlet,
                                                                          @Param("site") String site);

    List<Map<String, Object>> dealWithItOnScheduleByMonth(@Param("city") String city,
                                                          @Param("county") String county,
                                                          @Param("township") String township,
                                                          @Param("hamlet") String hamlet,
                                                          @Param("site") String site);

    List<Map<String, Object>> dealWithItOnScheduleByWeek(@Param("city") String city,
                                                         @Param("county") String county,
                                                         @Param("township") String township,
                                                         @Param("hamlet") String hamlet,
                                                         @Param("site") String site);

    List<Map<String, Object>> numberOfTasksByMonth(String city,
                                                   String county,
                                                   String township,
                                                   String hamlet,
                                                   String site);

    List<Map<String, Object>> taskNumberProcessingNumberProcessingRateByMonth(@Param("city") String city,
                                                                              @Param("county") String county,
                                                                              @Param("township") String township,
                                                                              @Param("hamlet") String hamlet,
                                                                              @Param("site") String site,
                                                                              @Param("startTime") Date startTime,
                                                                              @Param("endTime") Date endTime
    );

    List<Map<String, Object>> onTimeCompletionRateByMonth(@Param("city") String city,
                                                          @Param("county") String county,
                                                          @Param("township") String township,
                                                          @Param("hamlet") String hamlet,
                                                          @Param("site") String site);

    List<Map<String, Object>> taskNumberProcessingNumberProcessingRateByYear(@Param("city") String city,
                                                                             @Param("county") String county,
                                                                             @Param("township") String township,
                                                                             @Param("hamlet") String hamlet,
                                                                             @Param("site") String site,
                                                                             @Param("year") String year);

    List<Map<String, Object>> onTimeCompletionRateByWeek(@Param("city") String city,
                                                         @Param("county") String county,
                                                         @Param("township") String township,
                                                         @Param("hamlet") String hamlet,
                                                         @Param("site") String site);

    List<Map<String, Object>> getDisposalEfficiencyAnalysisByDay(@Param("city") String city,
                                                                 @Param("county") String county,
                                                                 @Param("township") String township,
                                                                 @Param("hamlet") String hamlet,
                                                                 @Param("site") String site,
                                                                 @Param("year") int year,
                                                                 @Param("month") int month,
                                                                 @Param("day") int day);

    List<Map<String, Object>> getDisposalEfficiencyAnalysisByMonth(@Param("city") String city,
                                                                   @Param("county") String county,
                                                                   @Param("township") String township,
                                                                   @Param("hamlet") String hamlet,
                                                                   @Param("site") String site,
                                                                   @Param("year") int year,
                                                                   @Param("month") int month);

    List<Map<String, Object>> getDisposalEfficiencyDailyDetailsByMonth(@Param("city") String city,
                                                                       @Param("county") String county,
                                                                       @Param("township") String township,
                                                                       @Param("hamlet") String hamlet,
                                                                       @Param("site") String site,
                                                                       @Param("year") int year,
                                                                       @Param("month") int month);

    List<Map<String, Object>> getDisposalEfficiencyAnalysisByYear(@Param("city") String city,
                                                                  @Param("county") String county,
                                                                  @Param("township") String township,
                                                                  @Param("hamlet") String hamlet,
                                                                  @Param("site") String site,
                                                                  @Param("year") int year);

    List<Map<String, Object>> getDisposalEfficiencyMonthlyDetailsByYear(@Param("city") String city,
                                                                        @Param("county") String county,
                                                                        @Param("township") String township,
                                                                        @Param("hamlet") String hamlet,
                                                                        @Param("site") String site,
                                                                        @Param("year") int year);
}