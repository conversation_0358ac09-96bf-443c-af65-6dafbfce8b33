package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.UserShiftGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserShiftGroupMapper extends BaseMapper<UserShiftGroup> {
    UserShiftGroup selectByUserId(@Param("userId") Integer userId);

    /**
     * 查询所有需要自动排班的劝导员的用户ID
     * @return 劝导员用户ID列表
     */
    List<Integer> selectAllCounselorsWithShiftGroup();
}