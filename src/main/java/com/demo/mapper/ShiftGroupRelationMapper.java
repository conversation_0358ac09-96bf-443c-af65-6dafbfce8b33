package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.Shift;
import com.demo.entity.ShiftGroupRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ShiftGroupRelationMapper extends BaseMapper<ShiftGroupRelation> {
    @Select("SELECT s.* FROM shift s JOIN shift_group_relation sgr ON s.id = sgr.shift_id WHERE sgr.group_id = #{groupId}")
    List<Shift> selectShiftsByGroupId(Integer groupId);

    @Select("SELECT * FROM shift_group_relation WHERE shift_id = #{shiftId}")
    List<ShiftGroupRelation> selectByShiftId(Integer shiftId);

    @Select("SELECT shift_id FROM shift_group_relation WHERE group_id = #{groupId}")
    List<Integer> selectShiftIdsByGroupId(@Param("groupId") Integer groupId);
}