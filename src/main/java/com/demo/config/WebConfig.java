package com.demo.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 配置静态资源访问
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置录制文件的静态资源访问
        registry.addResourceHandler("/recordings/**")
                .addResourceLocations("file:/home/<USER>/");
        
        // 配置默认静态资源
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }

    /**
     * 优化Tomcat配置，提高文件传输性能和连接稳定性
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                // 设置连接超时时间（60秒）
                connector.setProperty("connectionTimeout", "60000");
                // 设置最大连接数
                connector.setProperty("maxConnections", "8192");
                // 设置最大线程数
                connector.setProperty("maxThreads", "200");
                // 设置最小空闲线程数
                connector.setProperty("minSpareThreads", "10");
                // 设置连接保持活跃时间
                connector.setProperty("keepAliveTimeout", "30000");
                // 设置最大保持活跃请求数
                connector.setProperty("maxKeepAliveRequests", "100");
                // 禁用压缩（视频文件已经压缩过）
                connector.setProperty("compression", "off");
            });
        };
    }
}
