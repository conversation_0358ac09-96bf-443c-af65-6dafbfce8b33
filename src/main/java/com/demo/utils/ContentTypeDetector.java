package com.demo.utils;

import org.apache.tika.Tika;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@Component
public class ContentTypeDetector {
    private final Tika tika = new Tika();
    
    // 常见文件扩展名到MIME类型的映射
    private static final Map<String, String> MIME_TYPES = new HashMap<>();
    
    static {
        // 视频格式 (优先放在前面，因为这是主要用途)
        MIME_TYPES.put("mp4", "video/mp4");
        MIME_TYPES.put("avi", "video/x-msvideo");
        MIME_TYPES.put("wmv", "video/x-ms-wmv");
        MIME_TYPES.put("flv", "video/x-flv");
        MIME_TYPES.put("mov", "video/quicktime");
        MIME_TYPES.put("mkv", "video/x-matroska");
        MIME_TYPES.put("m4v", "video/mp4");
        MIME_TYPES.put("3gp", "video/3gpp");
        
        // 图片格式
        MIME_TYPES.put("jpg", "image/jpeg");
        MIME_TYPES.put("jpeg", "image/jpeg");
        MIME_TYPES.put("png", "image/png");
        MIME_TYPES.put("gif", "image/gif");
        MIME_TYPES.put("bmp", "image/bmp");
        MIME_TYPES.put("webp", "image/webp");
        
        // 文档格式
        MIME_TYPES.put("pdf", "application/pdf");
        MIME_TYPES.put("doc", "application/msword");
        MIME_TYPES.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        MIME_TYPES.put("xls", "application/vnd.ms-excel");
        MIME_TYPES.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        MIME_TYPES.put("ppt", "application/vnd.ms-powerpoint");
        MIME_TYPES.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
    }

    /**
     * 根据文件内容和文件名确定 Content-Type
     *
     * @param file 文件对象
     * @param fileName 文件名
     * @return 内容类型字符串
     */
    public String determineContentType(MultipartFile file, String fileName) {
        if (file == null || file.isEmpty() || fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件或文件名不能为空");
        }
        try {
            // 1. 优先使用文件扩展名判断
            String extension = getFileExtension(fileName).toLowerCase();
            String mimeType = MIME_TYPES.get(extension);
            // 2. 如果是已知的视频或图片扩展名，直接返回对应的MIME类型
            if (mimeType != null) {
                return mimeType;
            }
            // 3. 如果不是已知扩展名，才使用Tika检测
            String tikaType = tika.detect(file.getInputStream());
            // 4. 如果Tika检测出是视频类型，返回对应的MIME类型
            if (tikaType.startsWith("video/")) {
                // 对于视频文件，如果有扩展名但未在映射表中，添加到映射表
                if (!extension.isEmpty() && !MIME_TYPES.containsKey(extension)) {
                    MIME_TYPES.put(extension, tikaType);
                }
                return tikaType;
            }
            // 5. 如果既不是已知扩展名，Tika也检测不出或检测为二进制，返回通用类型
            if ("application/octet-stream".equals(tikaType)) {
                // 如果有扩展名是mp4，强制返回video/mp4
                if ("mp4".equals(extension)) {
                    return "video/mp4";
                }
                return "application/octet-stream";
            }
            return tikaType;

        } catch (Exception e) {
            // 6. 发生异常时，如果是mp4扩展名，返回video/mp4
            String extension = getFileExtension(fileName).toLowerCase();
            if ("mp4".equals(extension)) {
                return "video/mp4";
            }
            return "application/octet-stream";
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        return "";
    }

    /**
     * 检查文件类型是否允许
     */
    public boolean isAllowedFileType(String contentType, String... allowedTypes) {
        if (contentType == null || allowedTypes == null) {
            return false;
        }
        for (String allowedType : allowedTypes) {
            if (contentType.toLowerCase().startsWith(allowedType.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否是视频文件
     */
    public boolean isVideoFile(String contentType) {
        return contentType != null && contentType.startsWith("video/");
    }

    /**
     * 检查是否是图片文件
     */
    public boolean isImageFile(String contentType) {
        return contentType != null && contentType.startsWith("image/");
    }
}