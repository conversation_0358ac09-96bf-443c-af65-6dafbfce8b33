package com.demo.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.IOException;

public class Base64MultipartFile implements MultipartFile {
    private final byte[] content;
    private final String originalFilename;

    public Base64MultipartFile(byte[] content, String originalFilename) {
        this.content = content;
        this.originalFilename = originalFilename;
    }

    @Override
    public String getName() {
        return "file";
    }

    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }

    @Override
    public String getContentType() {
        return "image/jpeg"; // 根据实际图片类型调整，例如 png 可改为 "image/png"
    }

    @Override
    public boolean isEmpty() {
        return content == null || content.length == 0;
    }

    @Override
    public long getSize() {
        return content.length;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return content;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(content);
    }

    @Override
    public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
        throw new UnsupportedOperationException("不支持此操作");
    }
}
