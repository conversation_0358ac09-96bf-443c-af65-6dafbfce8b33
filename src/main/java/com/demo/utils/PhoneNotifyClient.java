package com.demo.utils;

import okhttp3.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class PhoneNotifyClient {
    private final OkHttpClient client = new OkHttpClient();
    @Async
    public void sendPhoneNotify(String content, String phoneNumber) throws IOException {
        // 创建请求体
        MediaType JSON = MediaType.get("application/json; charset=utf-8");
        String json = "{"
                + "\"uid\": 6427,"
                + "\"box_id\": 106,"
                + "\"task_id\": 21767,"
                + "\"content\": \"" + content + "\","
                + "\"numbers\": \"" + phoneNumber + "\""
                + "}";
        RequestBody body = RequestBody.create(json, JSON);

        // 构造请求
        Request request = new Request.Builder()
                .url("http://jostong.com/phone_notify")
                .post(body)
                .build();
        System.out.println(json);
        // 执行请求并处理响应
        try (Response response = client.newCall(request).execute()) {
            System.out.println(response);
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            // 处理响应结果
            System.out.println(response.body().string());
        }
    }

    public void phoneNotify(String content, String phoneNumbers) throws IOException {
        try {
            new PhoneNotifyClient().sendPhoneNotify(content, phoneNumbers);
//            new PhoneNotifyClient().sendPhoneNotify("请您及时联系宜宾道安安防工程有限公司处理合同纠纷事宜。", "15066662626");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
