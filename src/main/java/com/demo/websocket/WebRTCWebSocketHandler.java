package com.demo.websocket;

import com.demo.service.RoomManager;
import com.demo.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

@Slf4j
@Component
public class WebRTCWebSocketHandler extends TextWebSocketHandler {
    
    @Autowired
    private RoomManager roomManager;
    @Autowired
    RedisUtils redisUtils;
    
    // 存储会话信息
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final Map<String, String> sessionRooms = new ConcurrentHashMap<>(); // session.id -> roomId
    private final Map<String, String> sessionClients = new ConcurrentHashMap<>(); // session.id -> clientId
    private final Map<String, Map<String, WebSocketSession>> roomSessions = new ConcurrentHashMap<>(); // roomId -> Map<clientId, session>

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String sessionId = session.getId();
        log.info("WebSocket 连接建立: {}", sessionId);
        sessions.put(sessionId, session);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) {
        String payload = message.getPayload();
        try {
            JSONObject jsonMessage = JSON.parseObject(payload);
            String type = jsonMessage.getString("type");
            String roomId = jsonMessage.getString("roomId");
            String clientId = jsonMessage.getString("clientId");
            // 处理心跳消息
            if ("heartbeat".equals(type)) {
                // 回复心跳
                JSONObject response = new JSONObject()
                    .fluentPut("type", "heartbeat")
                    .fluentPut("timestamp", System.currentTimeMillis());
                session.sendMessage(new TextMessage(response.toJSONString()));
                if(clientId.contains("PC")){
                    redisUtils.setEx(roomId, "true", 1, TimeUnit.MINUTES);
                }
                log.info("收到心跳消息: roomId={}, clientId={}", roomId, clientId);
                return;
            }
            log.debug("收到消息: type={}, roomId={}, clientId={}", type, roomId, clientId);
            // 保存会话信息
            String sessionId = session.getId();
            sessionRooms.put(sessionId, roomId);
            sessionClients.put(sessionId, clientId);
            
            // 更新房间会话映射
            Map<String, WebSocketSession> roomClients = roomSessions.computeIfAbsent(roomId, k -> new ConcurrentHashMap<>());
            roomClients.put(clientId, session);
            
            // 使用策略模式处理不同类型的消息
            MessageHandler handler = messageHandlers.get(type);
            if (handler != null) {
                handler.handle(roomId, clientId, jsonMessage, roomClients);
            } else {
                log.warn("未知消息类型: {}", type);
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
        }
    }
    
    // 消息处理器接口
    private interface MessageHandler {
        void handle(String roomId, String clientId, JSONObject message, Map<String, WebSocketSession> roomClients);
    }
    
    // 消息处理器映射
    private final Map<String, MessageHandler> messageHandlers = Map.of(
        "join", this::handleJoin,
        "offer", this::handleForward,
        "answer", this::handleForward,
        "ice-candidate", this::handleForward,
        "leave", this::handleLeave
    );
    
    private void handleJoin(String roomId, String clientId, JSONObject message, Map<String, WebSocketSession> roomClients) {
        log.info("客户端加入房间: roomId={}, clientId={}", roomId, clientId);
        roomManager.addClientToRoom(roomId, clientId);
        // 打印房间状态
        Set<String> clients = roomManager.getRoomClients(roomId);
        log.info("房间状态 - roomId: {}, 当前在线: {}, 成员列表: {}", 
            roomId, clients.size(), String.join(", ", clients));
        if(clientId.contains("PC")){
            redisUtils.setEx(roomId, "true", 1, TimeUnit.MINUTES);
        }
        notifyPeers(roomId, clientId, message);
    }
    
    private void handleForward(String roomId, String clientId, JSONObject message, Map<String, WebSocketSession> roomClients) {
        notifyPeers(roomId, clientId, message);
    }
    
    private void notifyPeers(String roomId, String fromClientId, JSONObject message) {
        Map<String, WebSocketSession> roomClients = roomSessions.get(roomId);
        if (roomClients != null) {
            roomClients.forEach((toClientId, toSession) -> {
                if (!toClientId.equals(fromClientId) && toSession.isOpen()) {
                    try {
                        toSession.sendMessage(new TextMessage(message.toString()));
                        log.debug("消息已转发: {} -> {}, type={}", fromClientId, toClientId, message.getString("type"));
                    } catch (IOException e) {
                        log.error("发送消息失败: {} -> {}", fromClientId, toClientId, e);
                        cleanupSession(toSession.getId());
                    }
                }
            });
        }
    }

    private void cleanupSession(String sessionId) {
        WebSocketSession session = sessions.remove(sessionId);
        String roomId = sessionRooms.remove(sessionId);
        String clientId = sessionClients.remove(sessionId);
        
        if (roomId != null && clientId != null) {
            Map<String, WebSocketSession> roomClients = roomSessions.get(roomId);
            if (roomClients != null) {
                roomClients.remove(clientId);
                roomManager.removeClientFromRoom(roomId, clientId);
                
                // 打印房间状态
                Set<String> remainingClients = roomManager.getRoomClients(roomId);
                log.info("成员离开后房间状态 - roomId: {}, 剩余在线: {}, 成员列表: {}", 
                    roomId, remainingClients.size(), String.join(", ", remainingClients));
                if(clientId.contains("PC")){
                    redisUtils.delete(roomId);
                }
                // 通知其他客户端
                JSONObject notification = new JSONObject()
                    .fluentPut("type", "peer-left")
                    .fluentPut("roomId", roomId)
                    .fluentPut("clientId", clientId);
                    
                notifyPeers(roomId, clientId, notification);
                
                // 如果房间为空，清理房间
                if (roomClients.isEmpty()) {
                    roomSessions.remove(roomId);
                    log.info("房间已清空并移除: {}", roomId);
                }
            }
        }
    }

    private void handleLeave(String roomId, String clientId, JSONObject message, Map<String, WebSocketSession> roomClients) {
        log.info("客户端主动离开房间: roomId={}, clientId={}", roomId, clientId);
        if(clientId.contains("PC")){
            redisUtils.delete(roomId);
        }
        // 从房间管理器中移除客户端
        roomManager.removeClientFromRoom(roomId, clientId);
        
        // 从房间会话中移除
        if (roomClients != null) {
            WebSocketSession leavingSession = roomClients.remove(clientId);
            if (leavingSession != null) {
                // 获取sessionId并清理相关映射
                String sessionId = leavingSession.getId();
                sessions.remove(sessionId);
                sessionRooms.remove(sessionId);
                sessionClients.remove(sessionId);
            }
            // 打印房间状态
            Set<String> remainingClients = roomManager.getRoomClients(roomId);
            log.info("成员主动离开后房间状态 - roomId: {}, 剩余在线: {}, 成员列表: {}", 
                roomId, remainingClients.size(), String.join(", ", remainingClients));
            // 如果房间为空，清理房间
            if (roomClients.isEmpty()) {
                roomSessions.remove(roomId);
                log.info("房间已清空并移除: {}", roomId);
            }
        }
        
        // 转发leave消息给房间内其他用户
        notifyPeers(roomId, clientId, message);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String sessionId = session.getId();
        String roomId = sessionRooms.get(sessionId);
        String clientId = sessionClients.get(sessionId);
        log.info("WebSocket 连接关闭: {}, 原因: {}", sessionId, status);
        if (roomId != null && clientId != null) {
            // 通知房间内其他人该成员离开
            Map<String, WebSocketSession> roomClients = roomSessions.get(roomId);
            if (roomClients != null) {
                roomClients.forEach((otherClientId, otherSession) -> {
                    if (!otherClientId.equals(clientId) && otherSession.isOpen()) {
                        try {
                            JSONObject notification = new JSONObject()
                                .fluentPut("type", "peer-left")
                                .fluentPut("roomId", roomId)
                                .fluentPut("clientId", clientId);
                            otherSession.sendMessage(new TextMessage(notification.toString()));
                            log.info("通知客户端 {} 成员 {} 离开", otherClientId, clientId);
                        } catch (IOException e) {
                            log.info("发送离开通知失败", e);
                        }
                    }
                });
            }
        }
        
        cleanupSession(sessionId);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        String sessionId = session.getId();
        String roomId = sessionRooms.get(sessionId);
        String clientId = sessionClients.get(sessionId);
        
        log.error("WebSocket传输错误 - 会话ID: {}, 房间: {}, 客户端: {}, 错误: {}", 
            sessionId, roomId, clientId, exception.getMessage(), exception);
        
        try {
            // 1. 通知房间其他用户
            if (roomId != null && clientId != null) {
                JSONObject notification = new JSONObject()
                    .fluentPut("type", "peer-error")
                    .fluentPut("roomId", roomId)
                    .fluentPut("clientId", clientId)
                    .fluentPut("error", "连接异常");
                notifyPeers(roomId, clientId, notification);
                if(clientId.contains("PC")){
                    redisUtils.delete(roomId);
                }
            }
            
            // 2. 清理资源
            cleanupSession(sessionId);
            
            // 3. 关闭问题连接
            if (session.isOpen()) {
                session.close(CloseStatus.SERVER_ERROR);
            }
        } catch (IOException e) {
            log.error("处理传输错误时发生异常", e);
        }
    }
} 