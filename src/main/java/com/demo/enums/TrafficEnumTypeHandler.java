package com.demo.enums;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@MappedTypes(TrafficEnum.class)
@MappedJdbcTypes(JdbcType.INTEGER)
public class TrafficEnumTypeHandler extends BaseTypeHandler<TrafficEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, TrafficEnum parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public TrafficEnum getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        int code = rs.getInt(columnName);
        return rs.wasNull() ? null : TrafficEnum.of(code);
    }

    @Override
    public TrafficEnum getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        int code = rs.getInt(columnIndex);
        return rs.wasNull() ? null : TrafficEnum.of(code);
    }

    @Override
    public TrafficEnum getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        int code = cs.getInt(columnIndex);
        return cs.wasNull() ? null : TrafficEnum.of(code);
    }
}
