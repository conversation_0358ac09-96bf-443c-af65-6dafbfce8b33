package com.demo.enums;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class VehicleTypeEnumConverter {

    public static class StringToVehicleTypeEnumConverter implements Converter<String, VehicleTypeEnum> {
        @Override
        public VehicleTypeEnum convert(String source) {
            return VehicleTypeEnum.fromString(source);
        }
    }

    public static class IntegerToVehicleTypeEnumConverter implements Converter<Integer, VehicleTypeEnum> {
        @Override
        public VehicleTypeEnum convert(Integer source) {
            return VehicleTypeEnum.of(source);
        }
    }
}
