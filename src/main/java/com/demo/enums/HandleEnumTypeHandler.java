package com.demo.enums;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@MappedTypes(HandleEnum.class)
@MappedJdbcTypes(JdbcType.INTEGER)
public class HandleEnumTypeHandler extends BaseTypeHandler<HandleEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, HandleEnum parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public HandleEnum getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        int code = rs.getInt(columnName);
        return rs.wasNull() ? null : HandleEnum.of(code);
    }

    @Override
    public HandleEnum getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        int code = rs.getInt(columnIndex);
        return rs.wasNull() ? null : HandleEnum.of(code);
    }

    @Override
    public HandleEnum getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        int code = cs.getInt(columnIndex);
        return cs.wasNull() ? null : HandleEnum.of(code);
    }
}
