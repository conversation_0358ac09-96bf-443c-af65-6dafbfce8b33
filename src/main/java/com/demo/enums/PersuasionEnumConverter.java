package com.demo.enums;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class PersuasionEnumConverter {

    public static class StringToPersuasionEnumConverter implements Converter<String, PersuasionEnum> {
        @Override
        public PersuasionEnum convert(String source) {
            return PersuasionEnum.fromString(source);
        }
    }

    public static class IntegerToPersuasionEnumConverter implements Converter<Integer, PersuasionEnum> {
        @Override
        public PersuasionEnum convert(Integer source) {
            return PersuasionEnum.of(source);
        }
    }
}
