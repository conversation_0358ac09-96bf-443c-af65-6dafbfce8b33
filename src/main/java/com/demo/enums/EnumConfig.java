package com.demo.enums;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class EnumConfig implements WebMvcConfigurer {

    @Autowired
    private ObjectMapper objectMapper;

    @PostConstruct
    public void registerCustomSerializers() {
        SimpleModule module = new SimpleModule();
        module.addSerializer(VehicleTypeEnum.class, new VehicleTypeEnumSerializer());
        module.addSerializer(PersuasionEnum.class, new PersuasionEnumSerializer());
        module.addSerializer(TrafficEnum.class, new TrafficEnumSerializer());
        module.addSerializer(HandleEnum.class, new HandleEnumSerializer());
        module.addSerializer(PlateColorEnum.class, new PlateColorEnumSerializer());
        objectMapper.registerModule(module);
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new VehicleTypeEnumConverter.StringToVehicleTypeEnumConverter());
        registry.addConverter(new VehicleTypeEnumConverter.IntegerToVehicleTypeEnumConverter());
        registry.addConverter(new PersuasionEnumConverter.StringToPersuasionEnumConverter());
        registry.addConverter(new PersuasionEnumConverter.IntegerToPersuasionEnumConverter());
        registry.addConverter(new TrafficEnumConverter.StringToTrafficEnumConverter());
        registry.addConverter(new TrafficEnumConverter.IntegerToTrafficEnumConverter());
        registry.addConverter(new HandleEnumConverter.StringToHandleEnumConverter());
        registry.addConverter(new HandleEnumConverter.IntegerToHandleEnumConverter());
        registry.addConverter(new PlateColorEnumConverter.StringToPlateColorEnumConverter());
        registry.addConverter(new PlateColorEnumConverter.IntegerToPlateColorEnumConverter());
    }
}
