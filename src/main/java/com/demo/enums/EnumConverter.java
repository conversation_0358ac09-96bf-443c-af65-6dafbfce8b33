package com.demo.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class EnumConverter {

    // 使用泛型方法确保类型安全
    public static <T extends Enum<T> & BaseEnum> List<Map<String, Object>> convertToMap(T[] enums) {
        return Arrays.stream(enums)
                .map(e -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("code", e.getCode());
                    map.put("desc", e.getDesc());
                    map.put("color", e.getColor());
                    return map;
                })
                .collect(Collectors.toList());
    }

    public static Map<String, List<Map<String, Object>>> getAllEnums() {
        return Map.of(
                "handle", convertToMap(HandleEnum.values()),
                "traffic", convertToMap(TrafficEnum.values()),
                "persuasion", convertToMap(PersuasionEnum.values()),
                "overcrowding", convertToMap(VehicleTypeEnum.values()),
                "plateColor", convertToMap(PlateColorEnum.values())
        );
    }
}
