package com.demo.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 超载相关的枚举
 *
 * <AUTHOR>
 */
@Getter
public enum VehicleTypeEnum  implements BaseEnum{
    OVERLOAD(0, "两轮车" ),
    NOT_OVERLOAD(1, "三轮车"),
    BATTERY_CART(2, "两轮摩托车");
    @EnumValue
    private final int code;
    @JsonValue
    private final String desc;
    VehicleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VehicleTypeEnum of(int code) {
        for (VehicleTypeEnum overcrowdingEnum : values()) {
            if (overcrowdingEnum.getCode() == code) {
                return overcrowdingEnum;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    public static VehicleTypeEnum fromString(String codeStr) {
        try {
            int code = Integer.parseInt(codeStr);
            return of(code);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid integer format for [" + codeStr + "]");
        }
    }

    @Override
    public String getColor() {
        return "";
    }
}
