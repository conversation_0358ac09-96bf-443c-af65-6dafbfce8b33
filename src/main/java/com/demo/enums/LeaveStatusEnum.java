package com.demo.enums;

import lombok.Getter;

/**
 * 请假状态枚举
 */
@Getter
public enum LeaveStatusEnum {
    
    PENDING(0, "待审批"),
    APPROVED(1, "已批准"),
    REJECTED(2, "已拒绝"),
    CANCELED(3, "已取消");
    
    private final Integer code;
    private final String desc;
    
    LeaveStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static String getDescByCode(Integer code) {
        for (LeaveStatusEnum status : LeaveStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        return null;
    }
} 