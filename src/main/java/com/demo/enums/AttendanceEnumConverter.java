package com.demo.enums;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class AttendanceEnumConverter {
    public static class StringToAssignEnumConverter implements Converter<String, AttendanceEnum> {
        @Override
        public AttendanceEnum convert(String source) {
            return AttendanceEnum.fromString(source);
        }
    }

    public static class IntegerToAssignEnumConverter implements Converter<Integer, AttendanceEnum> {
        @Override
        public AttendanceEnum convert(Integer source) {
            return AttendanceEnum.of(source);
        }
    }
}
