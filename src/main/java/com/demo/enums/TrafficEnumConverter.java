package com.demo.enums;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class TrafficEnumConverter {

    public static class StringToTrafficEnumConverter implements Converter<String, TrafficEnum> {
        @Override
        public TrafficEnum convert(String source) {
            return TrafficEnum.fromString(source);
        }
    }

    public static class IntegerToTrafficEnumConverter implements Converter<Integer, TrafficEnum> {
        @Override
        public TrafficEnum convert(Integer source) {
            return TrafficEnum.of(source);
        }
    }
}
