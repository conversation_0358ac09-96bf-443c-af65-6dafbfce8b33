package com.demo.enums;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class HandleEnumConverter {

    public static class StringToHandleEnumConverter implements Converter<String, HandleEnum> {
        @Override
        public HandleEnum convert(String source) {
            return HandleEnum.fromString(source);
        }
    }

    public static class IntegerToHandleEnumConverter implements Converter<Integer, HandleEnum> {
        @Override
        public HandleEnum convert(Integer source) {
            return HandleEnum.of(source);
        }
    }
}
