package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.util.SaResult;
import com.demo.config.Log;
import com.demo.entity.DTO.DeviceDTO;
import com.demo.entity.Device;
import com.demo.enums.BusinessType;
import com.demo.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.InvocationTargetException;

/**
 * 设备控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/device")
public class DeviceController {
    @Autowired
    DeviceService deviceService;

    /**
     * 查询设备
     */
    @SaCheckPermission("/device/list/device/getDevice")
    @GetMapping("/getDevice")
    @Log(title = "查询设备", businessType = BusinessType.SELECT)
    public SaResult getDevice(DeviceDTO deviceDTO) {
        return deviceService.getDevice(deviceDTO);
    }

    /**
     * 添加设备
     */
    @SaCheckPermission("/device/list/device/addDevice")
    @PostMapping("/addDevice")
    @Log(title = "添加设备", businessType = BusinessType.INSERT)
    public SaResult addDevice(@RequestBody DeviceDTO deviceDTO) throws InvocationTargetException, IllegalAccessException {
        return deviceService.addDevice(deviceDTO);
    }

    /**
     * 删除设备
     */
    @SaCheckPermission("/device/list/device/deleteDevice")
    @DeleteMapping("/deleteDevice")
    @Log(title = "删除设备", businessType = BusinessType.DELETE)
    public SaResult deleteDevice(Integer id) {
        return SaResult.data(deviceService.removeById(id));
    }

    /**
     * 修改设备
     */
    @SaCheckPermission("/device/list/device/updateDevice")
    @PutMapping("updateDevice")
    @Log(title = "修改设备", businessType = BusinessType.UPDATE)
    public SaResult updateDevice(@RequestBody Device device) {
        return deviceService.updateDevice(device);
    }

    /**
     * 按地点分组查询设备
     *
     * @param city     市
     * @param county   区县
     * @param township 镇街道
     * @param hamlet   社区/村
     * @param site     点位
     * @param state    设备状态（可选）：0-正常，1-故障，2-维修中
     * @return 按地点分组的设备列表
     */
    @GetMapping("/getDevicesByLocation")
    public SaResult getDevicesByLocation(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam(required = false) Integer state) {
        return deviceService.getDevicesByLocation(city, county, township, hamlet, site, state);
    }

    /**
     * 上报设备在线情况
     */
    @SaIgnore
    @PostMapping("/equipmentOnline")
    public SaResult equipmentOnline(@RequestParam(value = "token", required = true) String token,
                                    @RequestParam(value = "equipmentNumber", required = true) String equipmentNumber,
                                    @RequestParam(value = "equipmentOnline", required = true) Boolean equipmentOnline,
                                    @RequestParam(value = "normal", required = false) String[] normal,
                                    @RequestParam(value = "abnormal", required = false) String[] abnormal) {
        if (!"9999".equals(token)) {
            return SaResult.error("token错误");
        }
        return deviceService.equipmentOnline(equipmentNumber, equipmentOnline, normal, abnormal);
    }

    /**
     * 获取点位设备数据并按照点位分组
     */
    @GetMapping("/getsThePointDevice")
    public SaResult getsThePointDevice(@RequestParam(required = false) String city,
                                       @RequestParam(required = false) String county,
                                       @RequestParam(required = false) String township,
                                       @RequestParam(required = false) String hamlet,
                                       @RequestParam(required = false) String site) {
        return deviceService.getsThePointDevice(city, county, township, hamlet, site);
    }

    /**
     * 通过设备编号获取摄像机拉流地址
     */
    @GetMapping("/getCameraUrl")
    public SaResult getCameraUrl(@RequestParam(required = true) String equipmentNumber) {
        return deviceService.getCameraUrl(equipmentNumber);
    }

    /**
     * 路口设备获取配置参数
     */
    @SaIgnore
    @GetMapping("/getRoadCrossingDeviceConfig")
    public SaResult getRoadCrossingDeviceConfig(@RequestParam(required = true) String equipmentNumber, @RequestParam(required = true) String token) {
        if (!"9999".equals(token)) {
            return SaResult.error("token错误");
        }
        return deviceService.getRoadCrossingDeviceConfig(equipmentNumber);
    }

    /**
     * 是否立即更新设备配置
     */
    @SaIgnore
    @GetMapping("/updateRoadCrossingDeviceConfig")
    public String updateRoadCrossingDeviceConfig(@RequestParam(required = true) String equipmentNumber, @RequestParam(required = true) String token) {
        if (!"9999".equals(token)) {
            return "token错误";
        }
        return deviceService.updateRoadCrossingDeviceConfig(equipmentNumber);
    }
}
