//package com.demo.controller;
//
//import com.demo.entity.Employee;
//import com.demo.service.EmployeeService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/employees")
//public class EmployeeController {
//    @Autowired
//    private EmployeeService employeeService; // 注入员工服务
//
//    /**
//     * 获取所有员工
//     * @return 所有员工列表
//     */
//    @GetMapping
//    public List<Employee> getAllEmployees() {
//        return employeeService.getAllEmployees();
//    }
//
//    /**
//     * 根据ID获取员工
//     * @param id 员工ID
//     * @return 员工对象
//     */
//    @GetMapping("/{id}")
//    public Employee getEmployeeById(@PathVariable Long id) {
//        return employeeService.getEmployeeById(id);
//    }
//
//    /**
//     * 创建新员工
//     * @param employee 员工对象
//     * @return 保存后的员工对象
//     */
//    @PostMapping
//    public Employee createEmployee(@RequestBody Employee employee) {
//        return employeeService.saveEmployee(employee);
//    }
//
//    /**
//     * 更新员工信息
//     * @param id 员工ID
//     * @param employee 更新后的员工对象
//     * @return 更新后的员工对象
//     */
//    @PutMapping("/{id}")
//    public Employee updateEmployee(@PathVariable Long id, @RequestBody Employee employee) {
//        employee.setId(id);
//        return employeeService.updateEmployee(employee);
//    }
//
//    /**
//     * 删除员工
//     * @param id 员工ID
//     */
//    @DeleteMapping("/{id}")
//    public void deleteEmployee(@PathVariable Long id) {
//        employeeService.deleteEmployee(id);
//    }
//}