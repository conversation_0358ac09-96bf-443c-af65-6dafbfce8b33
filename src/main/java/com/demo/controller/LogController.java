package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.demo.config.Log;
import com.demo.entity.DTO.LogDTO;
import com.demo.enums.BusinessType;
import com.demo.service.SysOperLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 操作日志
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log")
@Slf4j
public class LogController {
    @Autowired
    SysOperLogService sysOperLogService;

    /**
     * 查询操作日志
     *
     * @param logDTO
     * @return
     */
    @SaCheckPermission("/system/system-log/log/selectLog")
    @RequestMapping("/selectLog")
    @Log(title = "查询操作日志", businessType = BusinessType.SELECT)
    public SaResult selectLog(LogDTO logDTO) {
        return sysOperLogService.selectLog(logDTO);
    }

    /**
     * 删除操作日志
     *
     * @param id
     * @return
     */
    @SaCheckPermission("/system/system-log/log/deleteLog")
    @RequestMapping("/deleteLog")
    @Log(title = "删除操作日志", businessType = BusinessType.DELETE)
    public SaResult deleteLog(Integer id) {
        sysOperLogService.removeById(id);
        return SaResult.ok("删除成功");

    }

    /**
     * 批量删除操作日志
     */
    @SaCheckPermission("/system/system-log/log/deleteLogs")
    @RequestMapping("/deleteLogs")
    @Log(title = "批量删除操作日志", businessType = BusinessType.DELETE)
    public SaResult deleteLogs(@RequestParam List<Integer> ids) {
        sysOperLogService.removeByIds(ids);
        return SaResult.ok("删除成功");
    }


}
