package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.entity.DTO.ExamineFaceDTO;
import com.demo.service.ExamineFaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 审核人脸
 */
@RestController
@RequestMapping("/examineFace")
public class ExamineFaceController {
    @Autowired
    ExamineFaceService examineFaceService;

    /**
     * 获取审核人脸列表
     */
    @GetMapping("/selectExamineFace")
    public SaResult selectExamineFace(@RequestParam String city,
                                      @RequestParam String county,
                                      @RequestParam String township,
                                      @RequestParam String hamlet,
                                      @RequestParam String site,
                                      @RequestParam String examine,
                                      @RequestParam Integer pageSize,
                                      @RequestParam Integer curPage) {
        return examineFaceService.selectExamineFace(city, county, township, hamlet, site, examine,pageSize, curPage);
    }

    /**
     * 查看点位下的人员
     */
    @GetMapping("/getUsers")
    public SaResult getUsers(@RequestParam String city,
                             @RequestParam String county,
                             @RequestParam String township,
                             @RequestParam String hamlet,
                             @RequestParam String site) {
        return examineFaceService.getUsers(city, county, township, hamlet, site);
    }

    /**
     * 审核人脸
     */
    @PostMapping("/examineFace")
    public SaResult examineFace(@RequestBody ExamineFaceDTO examineFaceDTO) {
        return examineFaceService.examineFace(examineFaceDTO);
    }
}
