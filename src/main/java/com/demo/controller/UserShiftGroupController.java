package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demo.config.Log;
import com.demo.entity.DTO.UserShiftGroupAssignmentRequest;
import com.demo.entity.Schedule;
import com.demo.entity.UserShiftGroup;
import com.demo.enums.BusinessType;
import com.demo.service.ScheduleService;
import com.demo.service.UserShiftGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 分配员工到班次组
 */
@RestController
@RequestMapping("/user/shift-group")
public class UserShiftGroupController {
    
    @Autowired
    private UserShiftGroupService userShiftGroupService;
    @Autowired
    ScheduleService scheduleService;
    /**
     * 选班组
     */
    @SaCheckPermission("/schedule-management/user/shift-group/assign")
    @Log(title = "选班组", businessType = BusinessType.UPDATE)
    @PostMapping("/assign")
    public SaResult assign(@RequestBody UserShiftGroupAssignmentRequest request) {
        try {
            // 检查员工是否已经存在于某个班组
            UserShiftGroup existingGroup = userShiftGroupService.getCurrentGroup(request.getUserId(), LocalDateTime.now());
            if (existingGroup != null) {
                // 如果员工已经存在于某个班组，更新班组信息
                userShiftGroupService.updateUserGroup(request.getUserId(), request.getGroupId(), request.getFixedShiftId(), request.getStartDate());
                // 删除旧排班
                scheduleService.remove(new LambdaQueryWrapper<Schedule>()
                        .eq(Schedule::getUserId, request.getUserId())
                        .ge(Schedule::getScheduleDate, DateUtil.today()));
            } else {
                // 如果员工不在任何班组，分配到新的班组
                userShiftGroupService.assignUserToGroup(
                    request.getUserId(),
                    request.getGroupId(),
                    request.getFixedShiftId(),
                    request.getStartDate(),
                    request.getEndDate()
                );
            }
            if (request.getEndDate() != null){
                long daysBetween = ChronoUnit.DAYS.between(request.getStartDate(), request.getEndDate());
                scheduleService.generateSchedule(request.getUserId(), request.getGroupId(), LocalDateTime.now(), (int)daysBetween);
            }else {
                // 生成未来一个月的排班
                scheduleService.generateSchedule(request.getUserId(), request.getGroupId(), LocalDateTime.now(), 30);
            }
            return SaResult.ok();
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }
} 