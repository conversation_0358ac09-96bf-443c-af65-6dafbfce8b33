package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.config.Log;
import com.demo.config.RelatedConfigurations;
import com.demo.entity.Relatedconfigurations;
import com.demo.enums.BusinessType;
import com.demo.service.RelatedconfigurationsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 全局变量控制层
 */
@RestController
@RequestMapping("/RelatedConfigurations")
public class RelatedConfigurationsController {
    @Autowired
    RelatedconfigurationsService relatedConfigurationsService;

    /**
     * 查询全局配置
     */
    @Log(title = "查询全局配置", businessType = BusinessType.SELECT)
    @RequestMapping("/getRelatedConfigurations")
    public SaResult getRelatedConfigurations() {
        return SaResult.data(RelatedConfigurations.relatedconfigurations);
    }
    /**
     * 修改全局配置
     */
    @RequestMapping("/updateRelatedConfigurations")
    @Log(title = "修改全局配置", businessType = BusinessType.UPDATE)
    public SaResult updateRelatedConfigurations(@RequestBody Relatedconfigurations relatedconfigurations) {
        relatedConfigurationsService.updateById(relatedconfigurations);
        RelatedConfigurations.relatedconfigurations = relatedconfigurations;
        return SaResult.ok("修改成功");
    }

}
