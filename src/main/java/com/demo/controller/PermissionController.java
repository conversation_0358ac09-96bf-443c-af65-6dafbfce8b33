package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.config.Log;
import com.demo.enums.BusinessType;
import com.demo.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限控制层
 */
@RestController
@RequestMapping("/permission")
public class PermissionController {
    @Autowired
    PermissionService permissionService;

    /**
     * 查询权限列表
     */
    @RequestMapping("/selectPermission")
    @Log(title = "查询权限列表", businessType = BusinessType.SELECT)
    public SaResult selectPermission() {
        return permissionService.selectPermission();
    }




}
