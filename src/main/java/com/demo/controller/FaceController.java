package com.demo.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.util.SaResult;
import com.demo.service.FaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人脸识别控制器
 */
@RestController
@RequestMapping("/face")
public class FaceController {
    @Autowired
    FaceService faceService;

    /**
     * 获取点位下的人脸信息
     * @return
     */
    @GetMapping("/getPointFace")
    @SaIgnore
    public SaResult getPointFace(String equipmentNumber, String token) {
        if (!"9999".equals(token)) {
            System.out.println("token:----" + token);
            return SaResult.error("token错误");
        }
        return faceService.getPointFace(equipmentNumber);
    }







}
