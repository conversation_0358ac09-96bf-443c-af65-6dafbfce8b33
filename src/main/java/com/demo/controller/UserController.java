package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demo.config.Log;
import com.demo.entity.DTO.*;
import com.demo.entity.Users;
import com.demo.enums.BusinessType;
import com.demo.service.UsersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
/**
 * sa-token用户控制层
 *
 * <AUTHOR>
@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {
    @Autowired
    UsersService usersService;

    /**
     * 登录接口
     */
    @RequestMapping("/login")
    @Log(title = "登录", businessType = BusinessType.ASSIGN)
    @SaIgnore
    public SaResult login(@RequestBody UserDTO userDTO) {
        String phone = userDTO.getPhone();
        String password = userDTO.getPassword();
        //根据用户名从数据库中查询
        Users users = usersService.selectOne(phone);
        if (users == null) {
            return SaResult.error("手机号不存在");
        }
        if (!users.getPassword().equals(password)) {
            return SaResult.error("密码错误");
        }
        if (users.getState() == 1) {
            return SaResult.error("账户被冻结,请联系管理员");
        }
        if (users.getState() == 2) {
            return SaResult.error("账户已被删除,请联系管理员");
        }
        //密码加密返回
        users.setPassword(SaSecureUtil.md5(users.getPassword()));
        // 第1步，先登录上
        StpUtil.login(users.getUserId());
        // 第2步，获取 Token  相关参数
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        //获取用户的角色
        List<String> roleList = StpUtil.getRoleList();
        // 获取：当前账号所拥有的权限集合
        List<String> permissionList = StpUtil.getPermissionList();
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("tokenInfo", tokenInfo);
        hashMap.put("user", users);
        hashMap.put("roleList", roleList);
        hashMap.put("permissionList", permissionList);
        // 第3步，返回给前端
        return SaResult.data(hashMap);
    }


    /**
     * 修改密码（密码修改后token会被踢下线）
     */
    @RequestMapping("/updatePassword")
    @Log(title = "修改密码", businessType = BusinessType.UPDATE)
    public SaResult updatePassword(@RequestBody UpdatePasswordDTO updatePasswordDTO) {
        Integer userId = updatePasswordDTO.getUserId();
        String password = updatePasswordDTO.getOldPassword();
        //根据用户名从数据库中查询
        Users users = usersService.getOne(new LambdaQueryWrapper<Users>().eq(Users::getUserId, userId));
        if (users == null) {
            return SaResult.error("手机号不存在");
        }
        if (!users.getPassword().equals(password)) {
            return SaResult.error("密码错误");
        }
        StpUtil.kickout(userId);
        //更新密码
        users.setPassword(updatePasswordDTO.getNewPassword());
        usersService.updateById(users);
        return SaResult.ok("密码修改成功");
    }

    /**
     * 管理员重置密码
     */
    @SaCheckPermission("/system/user/user/resetPassword")
    @RequestMapping("/resetPassword")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    public SaResult resetPassword(@RequestBody UpdatePasswordDTO updatePasswordDTO) {
        //根据用户名从数据库中查询
        Users users = usersService.getOne(new LambdaQueryWrapper<Users>().eq(Users::getUserId, updatePasswordDTO.getUserId()));
        StpUtil.kickout(updatePasswordDTO.getUserId());
        //更新密码
        users.setPassword(updatePasswordDTO.getNewPassword());
        usersService.updateById(users);
        return SaResult.ok("密码重置成功");
    }

    /**
     * 修改用户信息
     */
    @SaCheckPermission("/system/user/user/updateUsername")
    @RequestMapping("/updateUsername")
    @Log(title = "修改用户信息", businessType = BusinessType.UPDATE)
    public SaResult updateUsername(@RequestBody UpdateUser users) throws InvocationTargetException, IllegalAccessException {
        return  usersService.updateUser(users);
    }

    /**
     * 增加用户
     */
    @SaCheckPermission("/system/user/user/addUser")
    @RequestMapping("/addUser")
    @Log(title = "增加用户", businessType = BusinessType.INSERT)
    public SaResult addUser(@RequestBody AddUser addUser) throws InvocationTargetException, IllegalAccessException {

        return usersService.addUser(addUser);
    }


    /**
     * 删除用户
     */
    @SaCheckPermission("/system/user/user/deleteUser")
    @RequestMapping("/deleteUser")
    @Log(title = "删除用户", businessType = BusinessType.DELETE)
    public SaResult deleteUser(Integer id) {
        return SaResult.ok(usersService.deleteUser(id));
    }

    /**
     * 批量删除用户（）
     */
    @RequestMapping("/deleteUserList")
    @Log(title = "批量删除用户", businessType = BusinessType.DELETE)
    public SaResult deleteUserList(@RequestBody List<Integer> ids) {
        return SaResult.ok(usersService.deleteUserList(ids));
    }

    /**
     * 用户状态切换(状态传0我改为1,传1我改为0)
     */
    @SaCheckPermission("/system/user/user/frozenUser")
    @RequestMapping("/frozenUser")
    @Log(title = "冻结用户", businessType = BusinessType.STATUS)
    public SaResult frozenUser(@RequestBody StateSwitching stateSwitching) {
        return SaResult.ok(usersService.frozenUser(stateSwitching));
    }

    /**
     * 查询用户列表
     */
    @SaCheckPermission("/system/user/user/selectUsers")
    @RequestMapping("/selectUsers")
    @Log(title = "获取用户信息", businessType = BusinessType.SELECT)
    public SaResult list(@RequestBody SelectUsers selectUsers) {
        return usersService.selectUsers(selectUsers);
    }

    /**
     * 查询 Token 信息
     */
    @RequestMapping("/tokenInfo")
    public SaResult tokenInfo() {
        int loginIdAsInt = StpUtil.getLoginIdAsInt();
        //获取用户
        Users user = usersService.getById(loginIdAsInt);
        //获取用户的角色
        List<String> roleList = StpUtil.getRoleList();
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        HashMap<String, Object> hashMap = new HashMap<>();
        // 获取：当前账号所拥有的权限集合
        List<String> permissionList = StpUtil.getPermissionList();
        hashMap.put("tokenInfo", tokenInfo);
        hashMap.put("user", user);
        hashMap.put("roleList", roleList);
        hashMap.put("permissionList", permissionList);
        return SaResult.data(hashMap);
    }


    ///**
    // * 查询用户信息接口，需要具备自定义的‘user-query’权限
    // *
    // */
    //@SaCheckPermission("user-write")
    //@GetMapping("/findUsers")
    //public SaResult findUsers() {
    //    return SaResult.ok();
    //}

    /**
     * 创建用户的接口，需具备‘user-write’或'write'权限之一
     * SaMode.AND, 标注一组权限，会话必须全部具有才可通过校验。
     * SaMode.OR, 标注一组权限，会话只要具有其一即可通过校验。
     *
     */
    //@SaCheckPermission(value = {"user-write", "write"}, mode = SaMode.OR)
    //@GetMapping("/createUser")
    //public SaResult createUser() {
    //    /*
    //    // 查询权限信息 ，如果当前会话未登录，会返回一个空集合
    //    List<String> permissionList = StpUtil.getPermissionList();
    //    System.out.println("当前登录账号拥有的所有权限：" + permissionList);
    //
    //    // 查询角色信息 ，如果当前会话未登录，会返回一个空集合
    //    List<String> roleList = StpUtil.getRoleList();
    //    System.out.println("当前登录账号拥有的所有角色：" + roleList);
    //    */
    //    return SaResult.ok();
    //}

    ///**
    // * 删除用户
    // * 代表需要拥有角色 admin 才可以操作
    // * @return SaResult 操作结果，返回成功状态
    // */
    //@SaCheckRole("admin")
    //@GetMapping("/deleteUser")
    //public SaResult deleteUser(){
    //    return SaResult.ok();
    //}


}
