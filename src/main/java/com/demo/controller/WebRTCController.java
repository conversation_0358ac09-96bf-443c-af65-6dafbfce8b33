package com.demo.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.util.SaResult;
import com.demo.service.WebRTCService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * WebRTC信令服务器控制器
 * 用于处理WebRTC音视频通话的信令交换
 */
@Slf4j
@RestController
@RequestMapping("/webrtc")
public class WebRTCController {
    
    @Autowired
    private WebRTCService webRTCService;

    /**
     * 测试接口
     */
    @SaIgnore
    @GetMapping("/test")
    public SaResult test() {
        return SaResult.ok("WebRTC服务正常工作");
    }

    /**
     * 获取房间状态
     */
    @SaIgnore
    @GetMapping("/room-status")
    public SaResult getRoomStatus(@RequestParam String roomId) {
        Set<String> clients = webRTCService.getRoomClients(roomId);
        Map<String, Object> data = new HashMap<>();
        data.put("roomId", roomId);
        data.put("clients", clients);
        data.put("clientCount", clients.size());
        log.info("查询房间状态 - roomId: {}, 在线人数: {}", roomId, clients.size());
        return SaResult.data(data);
    }


    /**
     * 请求语音通话
     */
    @SaIgnore
    @GetMapping("/requestWebRTC")
    public SaResult requestWebRTC(@RequestParam String roomId) {
        return webRTCService.requestWebRTC(roomId);
    }
} 