package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.enums.EnumConverter;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 枚举
 */
@RestController
@RequestMapping("/numberEnums")
public class NumberEnumController {
    /**
     * 查询枚举
     */
    @GetMapping("/all")
    public SaResult getAllEnums() {
        return SaResult.data(Map.of("enums", EnumConverter.getAllEnums()));
    }
}
