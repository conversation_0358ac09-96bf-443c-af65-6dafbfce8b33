package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.demo.config.Log;
import com.demo.entity.DTO.IocationDTO;
import com.demo.entity.DTO.SelectIllegalRecords;
import com.demo.entity.IllegalRecords;
import com.demo.enums.*;
import com.demo.service.IllegalRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

/**
 * 违法数据控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/IllegalRecords")
@Slf4j
public class IllegalRecordsController {

    @Autowired
    IllegalRecordsService illegalRecordsService;

    /**
     * 上传违法数据及图片（由于路口没法登录所以解除拦截）
     */
    @SaIgnore
    @PostMapping("/add")
    public SaResult addIllegalRecords(
            @RequestParam(value = "files", required = false) MultipartFile[] files,
            @RequestParam(value = "illegalType", required = false) Integer illegalType,
            @RequestParam(value = "vehicleType", required = false) Integer vehicleType,
            @RequestParam(value = "numberOfPassengers", required = false) Integer numberOfPassengers,
            @RequestParam(value = "persuasiveBehavior", required = false) Integer persuasiveBehavior,
            @RequestParam(value = "equipmentNumber", required = false) String equipmentNumber,
            @RequestParam(value = "captureTime", required = false) String captureTime,
            @RequestParam(value = "token", required = false) String token,
            @RequestParam(value = "plateColor", required = false) Integer plateColor,
            @RequestParam(value = "plateNumber", required = false) String plateNumber,
            @RequestParam(value = "restartTime", required = false) String restartTime,
            @RequestParam(value = "vehicleId", required = false) Integer vehicleId,
            @RequestParam(value = "cameraName", required = false) String cameraName,
            @RequestParam(value = "vehicleIdTwo", required = false) Integer vehicleIdTwo,
            @RequestParam(value = "cameraNameTwo", required = false) String cameraNameTwo,
            @RequestParam(value = "is_coming1", required = false) Boolean isComing1,
            @RequestParam(value = "is_coming2", required = false) Boolean isComing2,
            @RequestParam(value = "tipsText", required = false) String tipsText,
            @RequestParam(value = "illegal_zone1", required = false) Integer[] illegalZone1,
            @RequestParam(value = "illegal_zone2", required = false) Integer[] illegalZone2
    ) throws InvocationTargetException, IllegalAccessException, IOException {
        if (!"9999".equals(token)) {
            System.out.println("token:----" + token);
            return SaResult.error("token错误");
        }
        if (illegalType == 0) {
            return SaResult.ok("正常车辆不处理");
        }
        IllegalRecords illegalRecords=new IllegalRecords();
        illegalRecords.setIllegalType(TrafficEnum.of(illegalType));
        illegalRecords.setVehicleType(VehicleTypeEnum.of(vehicleType));
        illegalRecords.setNumberOfPassengers(numberOfPassengers);
        illegalRecords.setPersuasiveBehavior(PersuasionEnum.of(persuasiveBehavior));
        illegalRecords.setEquipmentNumber(equipmentNumber);
        illegalRecords.setCaptureTime(DateUtil.parse(captureTime));
        illegalRecords.setPlateColor(PlateColorEnum.of(plateColor));
        illegalRecords.setPlateNumber(plateNumber);
        illegalRecords.setRestartTime(DateUtil.parse(restartTime));
        illegalRecords.setVehicleId(vehicleId);
        illegalRecords.setCameraName(cameraName);
        illegalRecords.setVehicleIdTwo(vehicleIdTwo);
        illegalRecords.setCameraNameTwo(cameraNameTwo);
        illegalRecords.setIsComing1(isComing1);
        illegalRecords.setIsComing2(isComing2);
        illegalRecords.setTipsText(tipsText);
        illegalRecords.setIllegalZone1(illegalZone1);
        illegalRecords.setIllegalZone2(illegalZone2);
        if (PlateColorEnum.BLUE.equals(illegalRecords.getPlateColor())&&!VehicleTypeEnum.NOT_OVERLOAD.equals(illegalRecords.getVehicleType())) {
            illegalRecords.setVehicleType(VehicleTypeEnum.BATTERY_CART);
        }
        return illegalRecordsService.addIllegalRecords(illegalRecords, files);
    }

    /**
     * 上传有效劝导相关视频
     */
    @SaIgnore
    @PostMapping("/addVideo")
    public SaResult addVideo(@RequestParam(value = "token", required = false) String token,
                             @RequestParam(value = "file", required = false) MultipartFile[] file,
                             @RequestParam(value = "restartTime", required = false) String restartTime,
                             @RequestParam(value = "vehicleId", required = false) Integer vehicleId,
                             @RequestParam(value = "equipmentNumber", required = false) String equipmentNumber,
                             @RequestParam(value = "cameraName", required = false) String cameraName) {
        if (!"9999".equals(token)) {
            return SaResult.error("token错误");
        }
        // 创建一个Map来存储参数
        Map<String, Object> params = new HashMap<>();
        params.put("token", token);
        params.put("file", file != null ? file.length : 0); // 记录文件数量
        params.put("restartTime", restartTime);
        params.put("vehicleId", vehicleId);
        params.put("equipmentNumber", equipmentNumber);
        params.put("cameraName", cameraName);
//        JSONObject json = new JSONObject(params);
//        System.out.printf("JSON: %s", json);
//        log.info("JSON: {}", json);
        return illegalRecordsService.addVideo(file, DateUtil.parse(restartTime), vehicleId, cameraName, equipmentNumber);
    }

    /**
     * 延时上传车牌信息
     */
    @SaIgnore
    @PostMapping("/addPlateNumber")
    public SaResult addPlateNumber(@RequestParam(value = "token", required = false) String token,
                                   @RequestParam(value = "plateNumber", required = false) String plateNumber,
                                   @RequestParam(value = "plateColor", required = false) Integer plateColor,
                                   @RequestParam(value = "files", required = false) MultipartFile[] files,
                                   @RequestParam(value = "restartTime", required = false) String restartTime,
                                   @RequestParam(value = "vehicleId", required = false) Integer vehicleId,
                                   @RequestParam(value = "equipmentNumber", required = false) String equipmentNumber,
                                   @RequestParam(value = "cameraName", required = false) String cameraName,
                                   @RequestParam(value = "framePath", required = false) MultipartFile[] framePath,
                                   @RequestParam(value = "is_coming1", required = false) Boolean isComing1,
                                   @RequestParam(value = "is_coming2", required = false) Boolean isComing2) throws IOException {
        if (!"9999".equals(token)) {
            return SaResult.error("token错误");
        }
        return illegalRecordsService.addPlateNumber(plateNumber, DateUtil.parse(restartTime), vehicleId, equipmentNumber, cameraName, plateColor, files, framePath,isComing1,isComing2);
    }

//    /**
//     * 暂时确认违法准确性
//     */
//    @SaIgnore
//    @PostMapping("/addConfirm")
//    public SaResult addConfirm(
//            @RequestParam(value = "uuid", required = false) String uuid,
//            @RequestParam(value = "actualVehicleType", required = false) Integer actualVehicleType,
//            @RequestParam(value = "actualPlateColor", required = false) Integer actualPlateColor,
//            @RequestParam(value = "actualPlateNumber", required = false) String actualPlateNumber,
//            @RequestParam(value = "actualIllegalType", required = false) Integer actualIllegalType,
//            @RequestParam(value = "actualNumberOfPassengers", required = false) Integer actualNumberOfPassengers,
//            @RequestParam(value = "actualPersuasiveBehavior", required = false) Integer actualPersuasiveBehavior) {
//        // 验证输入参数
//        if (uuid == null || uuid.isEmpty()) {
//            return SaResult.error("uuid不能为空");
//        }
//        // 查询数据库中的记录
//        IllegalRecords record = illegalRecordsService.getById(uuid);
//        if (record == null) {
//            return SaResult.error("未找到对应的记录");
//        }
//
//        // 更新记录
//        if (actualVehicleType != null) {
//            record.setActualVehicleType(VehicleTypeEnum.of(actualVehicleType));
//        }
//        if (actualPlateColor != null) {
//            record.setActualPlateColor(PlateColorEnum.of(actualPlateColor));
//        }
//        if (actualPlateNumber != null) {
//            record.setActualPlateNumber(actualPlateNumber);
//        }
//        if (actualIllegalType != null) {
//            record.setActualIllegalType(TrafficEnum.of(actualIllegalType));
//        }
//        if (actualNumberOfPassengers != null) {
//            record.setActualNumberOfPassengers(actualNumberOfPassengers);
//        }
//        if (actualPersuasiveBehavior != null) {
//            record.setActualPersuasiveBehavior(PersuasionEnum.of(actualPersuasiveBehavior));
//        }
//        record.setAuditStatus(1);
//        // 保存更改
//        illegalRecordsService.updateById(record);
//
//        // 返回结果
//        return SaResult.ok("更新成功");
//    }

    /**
     * 测试网络流程度
     */
    @SaIgnore
    @GetMapping("/testNetworkFlowLevel")
    public SaResult testNetworkFlowLevel() {
        return SaResult.data(DateUtil.now());
    }

    ///**
    // * 指派劝导员
    // */
    //@RequestMapping("update")
    //public SaResult update(@RequestBody AssignACounselor assignACounselor) {
    //    return illegalRecordsService.assignACounselor(assignACounselor);
    //}

    ///**
    // * 查询劝导员（指派劝导员时用）
    // */
    //@GetMapping("selectProselytizer")
    //public SaResult selectProselytizer(IocationDTO ionationDTO) {
    //    return illegalRecordsService.selectProselytizer(ionationDTO);
    //}

    /**
     * 查询劝导员（展示用）
     */
    @GetMapping("selectProselytizerList")
    public SaResult selectProselytizerList(IocationDTO ionationDTO) {
        return illegalRecordsService.selectProselytizerList(ionationDTO);
    }

    /**
     * 查询违法数据
     */
    @SaCheckPermission("/illegal/list/IllegalRecords/selectIllegalRecords")
    @Log(title = "查询违法数据", businessType = BusinessType.SELECT)
    @GetMapping("selectIllegalRecords")
    public SaResult selectIllegalRecords(SelectIllegalRecords selectIllegalRecords) {
        return illegalRecordsService.selectIllegalRecords(selectIllegalRecords);
    }

    /**
     * 删除违法记录
     */
    @SaCheckPermission("/illegal/list/IllegalRecords/delete")
    @Log(title = "删除违法记录", businessType = BusinessType.DELETE)
    @RequestMapping("delete")
    public SaResult delete(String uuid) {
        return illegalRecordsService.removeById(uuid) ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }

    ///**
    // * 批量删除违法记录
    // */
    //@RequestMapping("deleteBatch")
    //public SaResult deleteBatch(@RequestBody List<String> uuid) {
    //    return illegalRecordsService.removeByIds(uuid) ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    //}

    /**
     * 查看已下派劝导员
     */
    @GetMapping("selectSentDown")
    public SaResult selectSentDown(String uuid) {
        return illegalRecordsService.selectSentDown(uuid);
    }

    /**
     * 查看详情
     */
    @SaCheckPermission("/illegal/list/IllegalRecords/selectDetails")
    @Log(title = "查看违法详情", businessType = BusinessType.SELECT)
    @GetMapping("selectDetails")
    public SaResult selectDetails(String uuid) {
        return illegalRecordsService.selectDetails(uuid);
    }

}
