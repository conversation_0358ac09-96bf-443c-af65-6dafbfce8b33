package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.demo.config.Log;
import com.demo.enums.BusinessType;
import com.demo.service.AlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Date;

/**
 * 报警控制
 */
@RestController
@RequestMapping("/alarm")
public class AlarmController {
    @Autowired
    AlarmService alarmService;

    /**
     * 查询报警
     */
    @SaCheckPermission("/warning/list/alarm/selectAlarm")
    @Log(title = "查询报警列表", businessType = BusinessType.SELECT)
    @GetMapping("/selectAlarm")
    public SaResult selectAlarm(String alarmType, Integer curPage, Integer pageSize,
                                @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime){
        return alarmService.selectAlarm(alarmType,startTime,endTime,curPage,pageSize);
    }

    /**
     * 手动报警
     */
    @GetMapping("/manualAlarm")
    public SaResult manualAlarm() throws IOException {
        return alarmService.manualAlarm();
    }

    /**
     * 确定报警
     */
    @GetMapping("/confirmAlarm")
    public SaResult confirmAlarm(Integer id){
        return alarmService.confirmAlarm(id);
    }

    /**
     * 获取手动报警
     */
    @GetMapping("/getManualOperation")
    public SaResult getManualOperation(){
        return alarmService.getManualOperation();
    }

    /**
     * 查看路口视频
     */
    @GetMapping("/getIntersection")
    public SaResult getIntersection(Integer id){
        return alarmService.getIntersection(id);
    }

    /**
     * 获取报警提示
     */
    @GetMapping("/getAlarmPrompt")
    public SaResult getAlarmPrompt(@RequestParam(required = false) String city,
                                   @RequestParam(required = false) String county,
                                   @RequestParam(required = false) String township,
                                   @RequestParam(required = false) String hamlet,
                                   @RequestParam(required = false) String site){
        return alarmService.getAlarmPrompt(city,county,township,hamlet,site);
    }


}
