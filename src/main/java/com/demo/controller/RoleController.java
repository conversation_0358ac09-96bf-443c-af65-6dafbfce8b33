package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.demo.config.Log;
import com.demo.entity.DTO.AssignRole;
import com.demo.entity.DTO.RoleDTO;
import com.demo.entity.DTO.SelectRoles;
import com.demo.enums.BusinessType;
import com.demo.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.InvocationTargetException;

/**
 * 角色管理控制
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/role")
@Slf4j
public class RoleController {
    @Autowired
    RoleService roleService;

    /**
     * 查询角色列表(添加用户处用,无分页)
     */
    @Log(title = "查询角色列表(未包含权限返回)", businessType = BusinessType.SELECT)
    @RequestMapping("/selectRoleList")
    public SaResult selectRoleList() {
        return roleService.selectRoleList();
    }

    /**
     * 查询角色列表
     */
    @SaCheckPermission("/system/system-role/role/selectRoles")
    @Log(title = "查询角色列表", businessType = BusinessType.SELECT)
    @RequestMapping("/selectRoles")
    public SaResult selectRoles(@ModelAttribute SelectRoles selectRoles) {
        return roleService.selectRoles(selectRoles);
    }

    /**
     * 增加角色并赋予权限给该角色(ID不传其余都穿)
     */
    @SaCheckPermission("/system/system-role/role/addRole")
    @Log(title = "增加角色", businessType = BusinessType.INSERT)
    @RequestMapping("/addRole")
    public SaResult addRole(@RequestBody RoleDTO roleDTO) throws InvocationTargetException, IllegalAccessException {
        return roleService.addRole(roleDTO);
    }

    /**
     * 查询角色拥有的权限
     */
    @SaCheckPermission("/system/system-role/role/selectRoles")
    @RequestMapping("/selectRolePermission")
    @Log(title = "查询角色拥有的权限", businessType = BusinessType.SELECT)
    public SaResult selectRolePermission(Integer id) {
        return roleService.selectPermissionList(id);
    }

    /**
     * 编辑角色
     */
    @SaCheckPermission("/system/system-role/role/updateRole")
    @Log(title = "编辑角色", businessType = BusinessType.UPDATE)
    @RequestMapping("/updateRole")
    public SaResult updateRole(@RequestBody RoleDTO roleDTO) throws InvocationTargetException, IllegalAccessException {
        return roleService.updateRole(roleDTO);
    }

    /**
     * 删除角色
     */
    @SaCheckPermission("/system/system-role/role/deleteRole")
    @Log(title = "删除角色", businessType = BusinessType.DELETE)
    @RequestMapping("/deleteRole")
    public SaResult deleteRole(Integer id) {
        return roleService.deleteById(id);
    }

//    /**
//     * 批量删除角色
//     */
//    @Log(title = "批量删除角色", businessType = BusinessType.DELETE)
//    @RequestMapping("/deleteRoleList")
//    public SaResult deleteRoleList(@RequestParam List<Integer> ids) {
//        return roleService.deleteByIds(ids);
//    }

    /**
     * 分配用户角色
     */
    @SaCheckPermission("/system/system-role/role/selectRoles")
    @Log(title = "分配用户角色", businessType = BusinessType.UPDATE)
    @RequestMapping("/assignRole")
    public SaResult assignRole(@RequestBody AssignRole assignRole) {
        return roleService.assignRole(assignRole);
    }
}
