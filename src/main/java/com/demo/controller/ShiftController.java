package com.demo.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demo.config.Log;
import com.demo.entity.Schedule;
import com.demo.entity.Shift;
import com.demo.entity.UserShiftGroup;
import com.demo.enums.BusinessType;
import com.demo.service.ScheduleService;
import com.demo.service.ShiftService;
import com.demo.service.UserShiftGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 班次
 */
@RestController
@RequestMapping("/shift")
public class ShiftController {

    @Autowired
    private ShiftService shiftService;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private UserShiftGroupService userShiftGroupService;

    /**
     * 添加新班次
     */
    @SaCheckPermission("/schedule-management/shift/add")
    @Log(title = "添加班次", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public SaResult add(@RequestBody Shift shift) {
        try {
            if (!shift.getStartTime().isBefore(shift.getEndTime())) {
                return SaResult.error("开始时间必须在结束时间之前");
            }
            shiftService.save(shift);
            return SaResult.data(shift);
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 获取所有班次列表
     */
    @GetMapping("/list")
    public SaResult list() {
        return SaResult.data(shiftService.list());
    }

    /**
     * 查询班次
     */
    @SaCheckPermission("/schedule-management/shift/listPage")
    @Log(title = "查询班次", businessType = BusinessType.SELECT)
    @GetMapping("/listPage")
    public SaResult listPage(@RequestParam(defaultValue = "1") int page,
                             @RequestParam(defaultValue = "10") int size) {
        IPage<Shift> pageRequest = new Page<>(page, size);
        return shiftService.selectPage(pageRequest);
    }

    /**
     * 删除班次
     */
    @SaCheckPermission("/schedule-management/shift/Id")
    @Log(title = "删除班次", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public SaResult delete(@PathVariable Integer id) {
        try {
            // 检查是否有排班依赖于该班次
            boolean hasDependencies = scheduleService.hasScheduleForShift(id);
            if (hasDependencies) {
                return SaResult.error("无法删除班次，因为有用户的排班依赖于此班次");
            }
            shiftService.removeById(id);
            return SaResult.ok("班次已删除");
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 修改班次
     */
    @SaCheckPermission("/schedule-management/shift/update")
    @Log(title = "修改班次", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public SaResult update(@RequestBody Shift shift) {
        try {
            if (!shift.getStartTime().isBefore(shift.getEndTime())) {
                return SaResult.error("开始时间必须在结束时间之前");
            }
            // 检查是否有排班依赖于该班次
            boolean hasDependencies = scheduleService.hasScheduleForShift(shift.getId());
            // 更新班次
            shiftService.updateById(shift);
            if (hasDependencies) {
                // 获取所有依赖于该班次的用户ID
                List<Integer> userIds = scheduleService.getUserIdsByShiftId(shift.getId());
                for (Integer userId : userIds) {
                    // 删除旧排班
                    scheduleService.remove(new LambdaQueryWrapper<Schedule>()
                            .eq(Schedule::getUserId, userId)
                            .eq(Schedule::getShiftId, shift.getId())
                            .ge(Schedule::getScheduleDate, new Date()));
                    // 重新生成排班
                    UserShiftGroup userGroup = userShiftGroupService.getCurrentGroup(userId, LocalDateTime.now()
                    );
                    if (userGroup != null) {
                        scheduleService.generateSchedule(userId, userGroup.getGroupId(), LocalDateTime.now()
                                , 30);
                    }
                }
            }

            return SaResult.ok("班次已修改");
        } catch (RuntimeException e) {
            return SaResult.error(e.getMessage());
        }
    }
}
