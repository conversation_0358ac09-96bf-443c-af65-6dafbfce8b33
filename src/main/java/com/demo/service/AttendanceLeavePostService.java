package com.demo.service;

import com.demo.entity.Attendance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class AttendanceLeavePostService {
    
    @Autowired
    private LeavePostService leavePostService;
    

    
    /**
     * 处理打卡时的脱岗记录
     */
    @Transactional
    public void handleAttendanceCheckOut(Integer userId, Integer scheduleId, LocalDateTime time) {
        leavePostService.endLeavePost(userId, scheduleId, time);
    }

} 