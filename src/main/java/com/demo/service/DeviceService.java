package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.DTO.DeviceDTO;
import com.demo.entity.Device;

import java.lang.reflect.InvocationTargetException;
import java.util.Date;

public interface DeviceService extends IService<Device>{

    SaResult getDevice(DeviceDTO deviceDTO);

    SaResult addDevice(DeviceDTO deviceDTO) throws InvocationTargetException, IllegalAccessException;

    SaResult updateDevice(Device device);

    SaResult deviceCount();

    SaResult deviceOnline();

    SaResult getDeviceOverview(String city, String county, String township, String hamlet, String site);

    /**
     * 按地点分组查询设备
     */
    SaResult getDevicesByLocation(String city, String county, String township, 
            String hamlet, String site, Integer state);

    SaResult equipmentOnline(String equipmentNumber, Boolean equipmentOnline, String[] normal, String[] abnormal);

    SaResult getsThePointDevice(String city, String county, String township, String hamlet, String site);

    SaResult getCameraUrl(String equipmentNumber);

    SaResult getRoadCrossingDeviceConfig(String equipmentNumber);

    String updateRoadCrossingDeviceConfig(String equipmentNumber);

    /**
     * 记录设备状态变化
     */
    void recordDeviceStatusChange(Device device, Integer newState, String reason) ;

    /**
     * 判断设备是否有历史记录
     */
    boolean hasDeviceStatusHistory(String equipmentNumber, String ip);
}
