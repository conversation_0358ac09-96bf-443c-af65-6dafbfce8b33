package com.demo.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.TreeNode;
import com.demo.entity.Users;
import com.demo.mapper.TreeNodeMapper;
import com.demo.mapper.UsersMapper;
import com.demo.service.TreeNodeService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class TreeNodeServiceImpl extends ServiceImpl<TreeNodeMapper, TreeNode> implements TreeNodeService {
    @Autowired
    TreeNodeMapper treeNodeMapper;
    @Autowired
    UsersMapper usersMapper;
    @Override
    public SaResult selectTreeNode() {
        List<String> roleList = StpUtil.getRoleList(); // 获取：当前账号的角色集合
        int loginIdAsInt = StpUtil.getLoginIdAsInt(); // 获取当前会话账号id, 并转换为int类型
        Users users = usersMapper.selectById(loginIdAsInt);
        // 确定顶级节点
        TreeNode topLevelNode = determineTopLevelNode(roleList, users);
        if (topLevelNode == null) {
            return SaResult.error("无法确定顶级节点");
        }
        // 查询顶级节点及其所有子节点
        List<TreeNode> allNodes = getAllChildNodes(topLevelNode.getId());
        // 构建树形结构
        Map<Integer, TreeNode> nodeMap = new HashMap<>();
        for (TreeNode node : allNodes) {
            nodeMap.put(node.getId(), node);
        }
        // 确保顶级节点在结果中
        if (!nodeMap.containsKey(topLevelNode.getId())) {
            nodeMap.put(topLevelNode.getId(), topLevelNode);
            allNodes.add(topLevelNode);
        }

        List<TreeNode> rootNodes = new ArrayList<>();
        for (TreeNode node : allNodes) {
            if (node.getParentId().equals(topLevelNode.getId())) {
                rootNodes.add(node);
            } else {
                TreeNode parentNode = nodeMap.get(node.getParentId());
                if (parentNode != null) {
                    if (parentNode.getChildList() == null) {
                        parentNode.setChildList(new ArrayList<>());
                    }
                    parentNode.getChildList().add(node);
                    //node.setLevel(parentNode.getLevel() + 1);
                }
            }
        }
        // 将顶级节点作为根节点
        topLevelNode.setChildList(rootNodes);
        //topLevelNode.setLevel(0);
        return SaResult.data(Collections.singletonList(topLevelNode));
    }

    private TreeNode determineTopLevelNode(List<String> roleList, Users user) {
        // 根据用户角色和位置信息确定顶级节点
        if (roleList.contains("city") && StringUtils.isNotBlank(user.getCity())) {
            return treeNodeMapper.selectOne(new QueryWrapper<TreeNode>().eq("label", user.getCity()));
        } else if (roleList.contains("county") && StringUtils.isNotBlank(user.getCounty())) {
            return treeNodeMapper.selectOne(new QueryWrapper<TreeNode>().eq("label", user.getCounty()));
        } else if (roleList.contains("township") && StringUtils.isNotBlank(user.getTownship())) {
            return treeNodeMapper.selectOne(new QueryWrapper<TreeNode>().eq("label", user.getTownship()));
        } else if (roleList.contains("hamlet") && StringUtils.isNotBlank(user.getHamlet())) {
            return treeNodeMapper.selectOne(new QueryWrapper<TreeNode>().eq("label", user.getHamlet()));
        } else if (roleList.contains("site") && StringUtils.isNotBlank(user.getSite())) {
            return treeNodeMapper.selectOne(new QueryWrapper<TreeNode>().eq("label", user.getSite()));
        }
        return null;
    }

    private List<TreeNode> getAllChildNodes(Integer parentId) {
        // 递归查询所有子节点
        List<TreeNode> nodes = treeNodeMapper.selectList(new QueryWrapper<TreeNode>().eq("parentId", parentId));
        List<TreeNode> allNodes = new ArrayList<>(nodes);
        for (TreeNode node : nodes) {
            allNodes.addAll(getAllChildNodes(node.getId()));
        }
        return allNodes;
    }

    @Override
    public SaResult addTreeNode(TreeNode node) {
        if (node.getParentId() ==0) {
            node.setLevel(0);
            treeNodeMapper.insert(node);
            return SaResult.ok("节点新增成功");
        }
        // 检查父节点的层级
        QueryWrapper<TreeNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", node.getParentId());
        TreeNode treeNode = treeNodeMapper.selectOne(queryWrapper);
        if (treeNode != null && treeNode.getLevel() >= 4) {
            return SaResult.error("节点层级已到达最大限制");
        }
        node.setLevel(treeNode.getLevel() + 1);
        int insertCount = treeNodeMapper.insert(node);
        if (insertCount > 0) {
            return SaResult.ok("节点新增成功");
        } else {
            return SaResult.error("节点新增失败");
        }
    }

    @Override
    public SaResult updateTreeNode(TreeNode node) {
        int updateCount = treeNodeMapper.updateById(node);
        if (updateCount > 0) {
            return SaResult.ok("节点更新成功");
        } else {
            return SaResult.error("节点更新失败");
        }
    }

    @Override
    @Transactional // 添加事务注解
    public SaResult deleteTreeNode(Integer id) {
        // 先删除子节点
        deleteChildren(id);
        // 删除当前节点
        int deleteCount = treeNodeMapper.deleteById(id);
        if (deleteCount > 0) {
            return SaResult.ok("节点删除成功");
        } else {
            return SaResult.error("节点删除失败");
        }
    }

    private void deleteChildren(Integer parentId) {
        QueryWrapper<TreeNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parentId", parentId);
        List<TreeNode> children = treeNodeMapper.selectList(queryWrapper);
        for (TreeNode child : children) {
            deleteChildren(child.getId()); // 递归删除子节点
            treeNodeMapper.deleteById(child.getId());
        }
    }
}
