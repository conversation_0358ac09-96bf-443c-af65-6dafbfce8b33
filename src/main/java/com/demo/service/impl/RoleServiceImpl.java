package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.DTO.AssignRole;
import com.demo.entity.DTO.RoleDTO;
import com.demo.entity.DTO.SelectRoles;
import com.demo.entity.Role;
import com.demo.entity.RolePermission;
import com.demo.entity.RoleUsers;
import com.demo.mapper.RoleMapper;
import com.demo.mapper.RolePermissionMapper;
import com.demo.mapper.RoleUsersMapper;
import com.demo.service.RoleService;
import com.demo.service.RoleUsersService;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.demo.utils.PingYing.getPingYin;

@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {
    @Autowired
    RoleMapper roleMapper;
    @Autowired
    RoleUsersMapper roleUsersMapper;
    @Autowired
    RolePermissionMapper rolePermissionMapper;
    @Autowired
    RoleUsersService RoleUsersService;
    @Override
    public SaResult selectRoles(SelectRoles selectRoles) {
        int offset = (selectRoles.getCurPage() - 1) * selectRoles.getPageSize();
        // 获取分页数据
        List<Role> roles = roleMapper.selectRolesWithPermissions(offset, selectRoles.getPageSize(), selectRoles.getRoleCode(), selectRoles.getRoleName());
        // 设置 total 条数
        long total = roleMapper.countRoles(selectRoles.getRoleCode(), selectRoles.getRoleName());
        IPage<Role> rolePage = new Page<>();
        rolePage.setTotal(total);
        rolePage.setRecords(roles);
        rolePage.setCurrent(selectRoles.getCurPage());
        rolePage.setSize(selectRoles.getPageSize());
        return SaResult.data(rolePage);
    }

    @Override
    @Transactional // 添加事务注解
    public SaResult addRole(RoleDTO roleDTO) throws InvocationTargetException, IllegalAccessException {
        Role role = new Role();
        BeanUtils.copyProperties(role,roleDTO);
        role.setRoleCode(getPingYin(role.getRoleName()));
        roleMapper.insert(role);
        Integer roleId = role.getRoleId();
        Integer[] permissions = roleDTO.getPermissions();
        for (Integer permission : permissions) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionId(permission);
            rolePermissionMapper.insert(rolePermission);
        }
        return SaResult.ok("增加角色成功");
    }

    @Override
    @Transactional // 添加事务注解
    public SaResult updateRole(RoleDTO roleDTO) {
        if (roleDTO.getPermissions() == null || roleDTO.getPermissions().length == 0) {
            return SaResult.error("角色权限不能为空");
        }
        QueryWrapper<RolePermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id",roleDTO.getRoleId());
        rolePermissionMapper.delete(queryWrapper);
        Integer roleId = roleDTO.getRoleId();
        Integer[] permissions = roleDTO.getPermissions();
        for (Integer permission : permissions) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionId(permission);
            rolePermissionMapper.insert(rolePermission);
        }
        return SaResult.ok("修改角色成功");
    }

    @Override
    public SaResult selectPermissionList(Integer id) {
        List<Map<String, Object>> rolePermissions= rolePermissionMapper.selectMap(id);
        return SaResult.data(rolePermissions);
    }

    @Override
    @Transactional // 添加事务注解
    public SaResult deleteById(Integer id) {
        roleMapper.deleteById(id);
        QueryWrapper<RolePermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id",id);
        rolePermissionMapper.delete(queryWrapper);
        return SaResult.ok("删除成功");
    }

    @Override
    @Transactional // 添加事务注解
    public SaResult deleteByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return SaResult.error("ID 列表不能为空");
        }
        // 删除角色
        roleMapper.deleteBatchIds(ids);
        // 删除角色权限关联
        QueryWrapper<RolePermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", ids);
        rolePermissionMapper.delete(queryWrapper);
        return SaResult.ok("批量删除成功");
    }

    @Override
    public SaResult selectRoleList() {
        List<Role> roles = roleMapper.selectList(null);
        return SaResult.data(roles);
    }

    @Override
    @Transactional // 添加事务注解
    public SaResult assignRole(AssignRole assignRole) {
        try {
            QueryWrapper<RoleUsers> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", assignRole.getUserId());
            roleUsersMapper.delete(queryWrapper);
            List<RoleUsers> roleUsersList = new ArrayList<>();
            for (Integer roleId : assignRole.getRoleIds()) {
                RoleUsers roleUsers = new RoleUsers();
                roleUsers.setUserId(assignRole.getUserId());
                roleUsers.setRoleId(roleId);
                roleUsersList.add(roleUsers);
            }
            if (roleUsersList.isEmpty()) {
                throw new IllegalArgumentException("请选择角色");
            }
            RoleUsersService.saveBatch(roleUsersList);
            return SaResult.ok("角色分配成功");
        } catch (IllegalArgumentException e) {
            // 捕获自定义异常并回滚事务
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return SaResult.error(e.getMessage());
        }
    }



}
