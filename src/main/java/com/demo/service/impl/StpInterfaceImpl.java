package com.demo.service.impl;

import cn.dev33.satoken.stp.StpInterface;
import com.demo.mapper.UsersMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 自定义权限加载接口实现类
 */
@Component    // 保证此类被 SpringBoot 扫描，完成 Sa-Token 的自定义权限验证扩展
public class StpInterfaceImpl implements StpInterface {
    @Autowired
    UsersMapper usersMapper;

    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        //        //        根据用户id从用户角色表中获取角色id
//        List<RoleUsers> rolesList = roleUsersService.selectRoleId(Integer.parseInt(loginId.toString()));
//        if (rolesList.isEmpty()) {
//            //    没有任何权限
//            return null;
//        }
//        List<String> list = new ArrayList<>();
//        rolesList.forEach(RoleUser -> {
//            // 根据角色id从角色权限表中获取权限id
//            List<RolePermission> rolePermissions = rolePermissionService.selectPermissionId(RoleUser.getRoleId());
//            // 根据权限id从权限表中获取权限名称
//            rolePermissions.forEach(permissionsId -> {
//                Permission permission = permissionService.selectById(permissionsId.getPermissionId());
//                list.add(permission.getPermissionCode());
//            });
//        });
        // 返回用户所有权限，
        return usersMapper.selectPermissionList(Integer.parseInt(loginId.toString()));
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        //        //        根据用户id从用户角色表中获取角色id
//        List<RoleUsers> rolesList = roleUsersService.selectRoleId(Integer.parseInt(loginId.toString()));
//        List<String> list = new ArrayList<>();
//        if (rolesList.isEmpty()) {
////            用户没有分配角色
//            return null;
//        }
//        rolesList.forEach(RoleUser -> {
//            Role role = roleService.selectById(RoleUser.getRoleId());
//            list.add(role.getRoleCode());
//        });
//        返回用户所有角色标识集合
        return usersMapper.selectroleList(Integer.parseInt(loginId.toString()));
    }

}
