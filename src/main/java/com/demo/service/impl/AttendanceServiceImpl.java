package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.*;
import com.demo.enums.AttendanceEnum;
import com.demo.enums.LeaveStatusEnum;
import com.demo.mapper.AttendanceMapper;
import com.demo.mapper.DeviceMapper;
import com.demo.mapper.LeaveMapper;
import com.demo.mapper.UsersMapper;
import com.demo.service.AttendanceService;
import com.demo.service.ScheduleService;
import com.demo.service.ShiftService;
import com.demo.utils.MinioUtil;
import com.demo.utils.RedisUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.demo.service.AttendanceLeavePostService;
import lombok.extern.slf4j.Slf4j;
import com.demo.service.LeavePostService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;


@Slf4j
@Service
public class AttendanceServiceImpl extends ServiceImpl<AttendanceMapper, Attendance> implements AttendanceService {

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private ShiftService shiftService;

    @Autowired
    private AttendanceMapper attendanceMapper;

    @Autowired
    private LeaveMapper leaveMapper;

    @Autowired
    private UsersMapper usersMapper;
    @Autowired
    MinioUtil minioUtil;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private AttendanceLeavePostService attendanceLeavePostService;

    @Autowired
    private LeavePostService leavePostService;
    @Autowired
    private RedisUtils redisUtils;

    @Override
    public Attendance clock(Integer userId) {
        LocalDateTime now = LocalDateTime.now();

        // 检查用户是否处于请假状态
        if (isUserOnLeave(userId, now)) {
            throw new RuntimeException("您当前处于请假状态，无需打卡");
        }

        // 先查找是否有未签退的记录
        Attendance attendance = this.getOne(new LambdaQueryWrapper<Attendance>()
                .eq(Attendance::getUserId, userId)
                .isNull(Attendance::getCheckOutTime)
                .orderByDesc(Attendance::getCreateTime)
                .last("LIMIT 1"));

        if (attendance != null) {
            // 有未签退记录，执行签退操作
            return checkOut(attendance, now);
        } else {
            // 没有未签退记录，执行签到操作
            return checkIn(userId, now);
        }
    }

    // 内部签到方法
    private Attendance checkIn(Integer userId, LocalDateTime now) {
        // 检查用户是否处于请假状态
        if (isUserOnLeave(userId, now)) {
            throw new RuntimeException("您当前处于请假状态，无需打卡");
        }

        // 获取当天的排班
        Schedule schedule = scheduleService.getTodaySchedule(userId, now);
        if (schedule == null) {
            throw new RuntimeException("当前时间没有排班");
        }

        // 获取班次信息
        Shift shift = shiftService.getById(schedule.getShiftId());
        if (shift == null) {
            throw new RuntimeException("未找到对应的班次信息");
        }

        // 创建考勤记录
        Attendance attendance = new Attendance();
        attendance.setUserId(userId);
        attendance.setScheduleId(schedule.getId());
        attendance.setCheckInTime(now);
        attendance.setCreateTime(now);

        // 判断是否迟到
        LocalDateTime scheduleStart = schedule.getScheduleDate()
                .with(shift.getStartTime());
        if (now.isAfter(scheduleStart.plusMinutes(15))) { // 15分钟宽限期
            attendance.setStatus(AttendanceEnum.BE_LATE); // 使用枚举的code
        } else {
            attendance.setStatus(AttendanceEnum.NORMAL); // 使用枚举的code
        }

        this.save(attendance);
        return attendance;
    }

    // 内部签退方法
    private Attendance checkOut(Attendance attendance, LocalDateTime now) {
        // 获取排班和班次信息
        Schedule schedule = scheduleService.getById(attendance.getScheduleId());
        if (schedule == null) {
            throw new RuntimeException("未找到排班信息");
        }

        Shift shift = shiftService.getById(schedule.getShiftId());
        if (shift == null) {
            throw new RuntimeException("未找到班次信息");
        }

        // 更新签退时间
        attendance.setCheckOutTime(now);
        attendance.setUpdateTime(now);

        // 判断是否早退
        LocalDateTime scheduleEnd = schedule.getScheduleDate()
                .with(shift.getEndTime());
        if (now.isBefore(scheduleEnd)) {
            attendance.setStatus(AttendanceEnum.LEAVE_EARLY); // 使用枚举的code
        } else {
            attendance.setStatus(AttendanceEnum.NORMAL); // 使用枚举的code
        }

        this.updateById(attendance);

        // 直接结束脱岗记录，而不是发布事件
        attendanceLeavePostService.handleAttendanceCheckOut(attendance.getUserId(), attendance.getScheduleId(), now);

        return attendance;
    }

    /**
     * 检查用户是否处于请假状态
     */
    private boolean isUserOnLeave(Integer userId, LocalDateTime time) {
        log.info("检查用户 {} 在 {} 是否处于请假状态", userId, time);

        Date checkTime = Date.from(time.atZone(ZoneId.systemDefault()).toInstant());

        // 查询是否有已批准的请假记录覆盖当前时间
        LambdaQueryWrapper<Leave> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Leave::getUserId, userId)
                .eq(Leave::getStatus, LeaveStatusEnum.APPROVED.getCode())
                .le(Leave::getStartTime, checkTime)
                .ge(Leave::getEndTime, checkTime);

        long count = leaveMapper.selectCount(queryWrapper);

        boolean isOnLeave = count > 0;
        log.info("用户 {} 在 {} 的请假状态: {}", userId, time, isOnLeave ? "请假中" : "未请假");

        return isOnLeave;
    }

    @Override
    public List<Attendance> getEmployeeAttendance(Integer userId, LocalDateTime startDate, LocalDateTime endDate) {
        return this.list(new LambdaQueryWrapper<Attendance>()
                .eq(Attendance::getUserId, userId)
                .between(Attendance::getCreateTime, startDate, endDate)
                .orderByDesc(Attendance::getCreateTime));
    }

    @Override
    public Map<String, Object> getAttendanceStats(Integer userId, LocalDateTime startDate, LocalDateTime endDate) {
        List<Attendance> attendances = getEmployeeAttendance(userId, startDate, endDate);

        Map<String, Object> stats = new HashMap<>();
        stats.put("total", attendances.size());
        stats.put("normal", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.NORMAL).count());
        stats.put("late", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.BE_LATE).count());
        stats.put("early", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.LEAVE_EARLY).count());
        stats.put("absent", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.ABSENCE_FROM_DUTY).count());
        stats.put("leave", attendances.stream().filter(a -> a.getStatus() == AttendanceEnum.LEAVE).count());

        return stats;
    }

    @Override
    public Attendance getByScheduleId(Integer scheduleId) {
        return this.getOne(new LambdaQueryWrapper<Attendance>()
                .eq(Attendance::getScheduleId, scheduleId)
                .orderByDesc(Attendance::getCreateTime)
                .last("LIMIT 1"));
    }

    @Transactional
    public Attendance recordAttendance(Integer userId, Integer type, LocalDateTime time, Schedule currentSchedule, Shift shift) {
        // 检查用户是否处于请假状态
        if (isUserOnLeave(userId, time)) {
            throw new RuntimeException("您当前处于请假状态，无需打卡");
        }

        if (type == AttendanceEnum.NORMAL.getCode()) {  // 正常签到
            // 创建考勤记录
            Attendance attendance = new Attendance();
            attendance.setUserId(userId);
            attendance.setUserName(currentSchedule.getUserName());
            attendance.setScheduleId(currentSchedule.getId());
            attendance.setCheckInTime(time);
            attendance.setCreateTime(time);
            attendance.setUpdateTime(time);
            attendance.setType(type);

            // 判断是否迟到
            LocalDateTime scheduleStart = currentSchedule.getScheduleDate().with(shift.getStartTime());
            if (time.isAfter(scheduleStart.plusMinutes(15))) { // 15分钟宽限期
                attendance.setStatus(AttendanceEnum.BE_LATE);
            } else {
                attendance.setStatus(AttendanceEnum.NORMAL);
            }

            this.save(attendance);
            return attendance;

        } else if (type == AttendanceEnum.REPEAT_CHECK_IN.getCode()) {  // 重复签到
            // 查找最近的签到记录
            Attendance attendance = this.getOne(new LambdaQueryWrapper<Attendance>()
                    .eq(Attendance::getUserId, userId)
                    .eq(Attendance::getScheduleId, currentSchedule.getId())
                    .orderByDesc(Attendance::getCreateTime)
                    .last("LIMIT 1"));

            if (attendance == null) {
                throw new RuntimeException("未找到原始签到记录");
            }

            // 只更新修改时间和类型
            attendance.setUpdateTime(time);
            attendance.setType(type);
            this.updateById(attendance);
            return attendance;

        } else if (type == AttendanceEnum.CHECK_OUT.getCode() || type == AttendanceEnum.REPEAT_CHECK_OUT.getCode()) {  // 签退或重复签退
            // 查找最近的签到记录
            Attendance attendance = this.getOne(new LambdaQueryWrapper<Attendance>()
                    .eq(Attendance::getUserId, userId)
                    .eq(Attendance::getScheduleId, currentSchedule.getId())
                    .orderByDesc(Attendance::getCreateTime)
                    .last("LIMIT 1"));

            if (attendance == null) {
                throw new RuntimeException("未找到签到记录");
            }

            // 更新签退时间和状态
            attendance.setCheckOutTime(time);
            attendance.setUpdateTime(time);
            attendance.setType(type);

            // 判断是否早退
            LocalDateTime scheduleEnd = currentSchedule.getScheduleDate().with(shift.getEndTime());
            if (time.isBefore(scheduleEnd)) {
                attendance.setStatus(AttendanceEnum.LEAVE_EARLY);
            } else {
                attendance.setStatus(AttendanceEnum.NORMAL);
            }

            this.updateById(attendance);

            // 直接结束脱岗记录
            attendanceLeavePostService.handleAttendanceCheckOut(userId, currentSchedule.getId(), time);

            return attendance;
        } else {
            throw new RuntimeException("无效的打卡类型: " + type);
        }
    }

    @Override
    public boolean hasCheckInToday(Integer userId) {
        LocalDate today = LocalDate.now();
        // 查询当天的签到记录
        List<Attendance> records = attendanceMapper.selectList(new LambdaQueryWrapper<Attendance>()
                .eq(Attendance::getUserId, userId)
                .eq(Attendance::getType, 1) // 签到类型
                .ge(Attendance::getCheckInTime, today.atStartOfDay())
                .lt(Attendance::getCheckInTime, today.plusDays(1).atStartOfDay()));
        return !records.isEmpty();
    }

    @Override
    public Attendance getLastRecord(Integer userId) {
        // 查询用户的最后一次打卡记录
        return attendanceMapper.selectOne(new LambdaQueryWrapper<Attendance>()
                .eq(Attendance::getUserId, userId)
                .orderByDesc(Attendance::getCheckInTime)
                .last("LIMIT 1"));
    }


    @Override
    public List<Attendance> getEmployeeAttendance(String userName, LocalDateTime startDate, LocalDateTime endDate) {
        return this.list(new LambdaQueryWrapper<Attendance>()
                .like(StringUtils.isNotBlank(userName), Attendance::getUserName, userName)
                .between(Attendance::getCreateTime, startDate, endDate)
                .orderByDesc(Attendance::getCreateTime));
    }

    @Override
    @Transactional
    public void recordAttendance(Integer userId, Integer type, LocalDateTime time, Integer scheduleId,
                                 MultipartFile[] images, Schedule schedule, Shift shift) {
        try {
            // 使用原有方法记录打卡
            Attendance attendance = recordAttendance(userId, type, time, schedule, shift);

            // 如果是签退相关的类型，直接结束脱岗记录
            if (type == AttendanceEnum.CHECK_OUT.getCode() ||
                    type == AttendanceEnum.REPEAT_CHECK_OUT.getCode()) {
                attendanceLeavePostService.handleAttendanceCheckOut(userId, scheduleId, time);
            }

            // 处理图片上传
            if (images != null && images.length > 0) {
                String formatDate = DateUtil.formatDate(new Date());
                String address = userId.toString();
                String folderName = "打卡/" + formatDate + "/" + address;

                for (MultipartFile image : images) {
                    if (image != null && !image.isEmpty()) {
                        String fileName = image.getOriginalFilename();
                        String newImageUrl = minioUtil.uploadFile(image, fileName, folderName);
                        String existingImageUrl = attendance.getImageUrl();
                        if (existingImageUrl != null && !existingImageUrl.isEmpty()) {
                            attendance.setImageUrl(existingImageUrl + "," + newImageUrl);
                        } else {
                            attendance.setImageUrl(newImageUrl);
                        }
                        this.updateById(attendance);
                    }
                }
            }
        } catch (Exception e) {
            log.error("记录打卡失败", e);
            throw new RuntimeException("记录打卡失败: " + e.getMessage());
        }
    }

    @Override
    public SaResult updateLeavePost(Device device) {
        // 查找对应区域的用户
        QueryWrapper<Users> usersQueryWrapper = new QueryWrapper<>();
        usersQueryWrapper.eq("city", device.getCity())
                .eq("county", device.getCounty())
                .eq("township", device.getTownship())
                .eq("hamlet", device.getHamlet())
                .eq("site", device.getSite());
        List<Users> users = usersMapper.selectList(usersQueryWrapper);
        if (users == null || users.isEmpty()) {
            return SaResult.error("用户不存在");
        }
        LocalDateTime nowDateTime = LocalDateTime.now();
        boolean hasUpdates = false;
        // 遍历所有用户，检查并更新他们的记录
        for (Users user : users) {
            // 查找用户当前的排班记录
            Schedule currentSchedule = scheduleService.getCurrentSchedule(user.getUserId());
            if (currentSchedule == null) {
                continue; // 跳过没有当前排班的用户
            }
            // 查找当前班次的考勤记录
            Attendance attendance = this.getOne(new LambdaQueryWrapper<Attendance>()
                    .eq(Attendance::getUserId, user.getUserId())
                    .eq(Attendance::getScheduleId, currentSchedule.getId())
                    .isNotNull(Attendance::getCheckInTime)  // 确保用户已经打卡
                    .isNull(Attendance::getCheckOutTime)    // 且未签退
                    .orderByDesc(Attendance::getCreateTime)
                    .last("LIMIT 1"));

            // 查找并处理未结束的脱岗记录
            LeavePost leavePost = leavePostService.getOne(new LambdaQueryWrapper<LeavePost>()
                    .eq(LeavePost::getUserId, user.getUserId())
                    .eq(LeavePost::getScheduleId, currentSchedule.getId())
                    .eq(LeavePost::getStatus, 0)
                    .orderByDesc(LeavePost::getCreateTime)
                    .last("LIMIT 1"));

            if (attendance != null) {
                // 已有打卡记录的用户，更新考勤记录的修改时间
                attendance.setUpdateTime(LocalDateTime.now());
                this.updateById(attendance);
                log.info("更新用户 {} 的考勤记录修改时间", user.getUserId());

                if (leavePost != null) {
                    // 结束脱岗记录
                    leavePostService.endLeavePost(user.getUserId(), currentSchedule.getId(), nowDateTime);
                    log.info("结束用户 {} 的脱岗记录", user.getUserId());
                }
                hasUpdates = true;
            } else if (leavePost != null) {
                // 没有打卡记录但有未结束的脱岗记录，自动创建签到记录
                log.info("用户 {} 没有打卡记录但有未结束的脱岗记录，自动创建签到记录", user.getUserId());

                // 获取班次信息
                Shift shift = shiftService.getById(currentSchedule.getShiftId());
                if (shift != null) {
                    // 创建签到记录
                    Attendance newAttendance = new Attendance();
                    newAttendance.setUserId(user.getUserId());
                    newAttendance.setUserName(user.getName());
                    newAttendance.setScheduleId(currentSchedule.getId());
                    newAttendance.setCheckInTime(nowDateTime);
                    newAttendance.setCreateTime(nowDateTime);
                    newAttendance.setUpdateTime(nowDateTime);
                    newAttendance.setType(AttendanceEnum.NORMAL.getCode());

                    // 判断是否迟到
                    LocalDateTime scheduleStart = currentSchedule.getScheduleDate().with(shift.getStartTime());
                    if (nowDateTime.isAfter(scheduleStart.plusMinutes(15))) { // 15分钟宽限期
                        newAttendance.setStatus(AttendanceEnum.BE_LATE);
                        log.info("用户 {} 自动签到，判定为迟到", user.getUserId());
                    } else {
                        newAttendance.setStatus(AttendanceEnum.NORMAL);
                        log.info("用户 {} 自动签到，判定为正常", user.getUserId());
                    }

                    this.save(newAttendance);
                    log.info("为用户 {} 自动创建签到记录成功", user.getUserId());

                    // 结束脱岗记录
                    leavePostService.endLeavePost(user.getUserId(), currentSchedule.getId(), nowDateTime);
                    log.info("结束用户 {} 的脱岗记录", user.getUserId());

                    hasUpdates = true;
                }
            }
        }

        if (!hasUpdates) {
            return SaResult.ok("没有需要更新的记录");
        }

        return SaResult.ok("考勤记录和脱岗记录已更新");
    }

    public List<Attendance> getLastRecordBatch(List<Integer> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用自定义SQL查询每个用户的最后一条记录
        return attendanceMapper.selectLastRecordBatch(userIds);
    }


}
