package com.demo.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.Schedule;
import com.demo.entity.UserShiftGroup;
import com.demo.mapper.ScheduleMapper;
import com.demo.mapper.UserShiftGroupMapper;
import com.demo.service.UserShiftGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserShiftGroupServiceImpl extends ServiceImpl<UserShiftGroupMapper, UserShiftGroup> 
        implements UserShiftGroupService {

    @Autowired
    private UserShiftGroupMapper userShiftGroupMapper;
    @Autowired
    ScheduleMapper scheduleMapper;
    @Override
    @Transactional
    public void assignUserToGroup(Integer userId, Integer groupId, Integer fixedShiftId, LocalDateTime startDate, LocalDateTime endDate) {
        // 检查员工是否已经在某个班组中
        UserShiftGroup existingGroup = this.getOne(new LambdaQueryWrapper<UserShiftGroup>()
                .eq(UserShiftGroup::getUserId, userId)
                .eq(UserShiftGroup::getStatus, 1) // 生效状态
                .le(UserShiftGroup::getStartDate, startDate)
                .and(wrapper -> wrapper
//                        .isNull(UserShiftGroup::getEndDate)
//                        .or()
                        .ge(UserShiftGroup::getEndDate, startDate))
                .last("LIMIT 1"));

        if (existingGroup != null) {
            // 删除之前的排班
            QueryWrapper<Schedule> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                    .ge("schedule_date", DateUtil.today());
            scheduleMapper.delete(queryWrapper);
            // 更新现有记录
            existingGroup.setGroupId(groupId);
            existingGroup.setFixedShiftId(fixedShiftId);
            existingGroup.setStartDate(startDate);
            existingGroup.setUpdateTime(LocalDateTime.now());
            existingGroup.setEndDate(endDate);
            this.updateById(existingGroup);
        } else {
            // 插入新记录
            UserShiftGroup userShiftGroup = new UserShiftGroup();
            userShiftGroup.setUserId(userId);
            userShiftGroup.setGroupId(groupId);
            userShiftGroup.setFixedShiftId(fixedShiftId);
            userShiftGroup.setStartDate(startDate);
            userShiftGroup.setEndDate(endDate);
            userShiftGroup.setStatus(1); // 设置为生效状态
            userShiftGroup.setCreateTime(LocalDateTime.now());
            userShiftGroup.setUpdateTime(LocalDateTime.now());
            this.save(userShiftGroup);
        }
    }

    @Override
    public UserShiftGroup getCurrentGroup(Integer userId, LocalDateTime date) {
        // 获取指定日期用户生效的班次组
        return this.getOne(new LambdaQueryWrapper<UserShiftGroup>()
                .eq(UserShiftGroup::getUserId, userId)
                .eq(UserShiftGroup::getStatus, 1)           // 生效状态
                .le(UserShiftGroup::getStartDate, date)     // 开始日期小于等于指定日期
                .and(wrapper -> wrapper
                        .isNull(UserShiftGroup::getEndDate) // 未结束
                        .or()
                        .ge(UserShiftGroup::getEndDate, date)) // 或结束日期大于等于指定日期
                .orderByDesc(UserShiftGroup::getCreateTime)
                .last("LIMIT 1"));
    }

    @Override
    public List<Integer> getGroupUsers(Integer groupId) {
        // 获取班次组下所有生效的用户ID列表
        return this.list(new LambdaQueryWrapper<UserShiftGroup>()
                .eq(UserShiftGroup::getGroupId, groupId)
                .eq(UserShiftGroup::getStatus, 1))
                .stream()
                .map(UserShiftGroup::getUserId)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserShiftGroup> getUserGroups(Integer userId, LocalDateTime date) {
        return userShiftGroupMapper.selectList(new LambdaQueryWrapper<UserShiftGroup>()
                .eq(UserShiftGroup::getUserId, userId)
                .eq(UserShiftGroup::getStatus, 1) // 生效状态
                .le(UserShiftGroup::getStartDate, date)
                .and(wrapper -> wrapper
                        .isNull(UserShiftGroup::getEndDate)
                        .or()
                        .ge(UserShiftGroup::getEndDate, date))
        );
    }

    @Override
    public void updateUserGroup(Integer userId, Integer groupId, Integer fixedShiftId, LocalDateTime startDate) {
        UserShiftGroup userShiftGroup = userShiftGroupMapper.selectByUserId(userId);
        if (userShiftGroup != null) {
            userShiftGroup.setGroupId(groupId);
            userShiftGroup.setFixedShiftId(fixedShiftId);
            userShiftGroup.setStartDate(startDate);
            userShiftGroupMapper.updateById(userShiftGroup);
        } else {
            throw new RuntimeException("未找到员工的班组信息");
        }
    }

    @Override
    public List<Integer> getAllCounselorsWithShiftGroup() {
        // 从数据库中获取所有需要自动排班的劝导员的用户ID
        return userShiftGroupMapper.selectAllCounselorsWithShiftGroup();
    }
} 