package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.VillageInformation;
import com.demo.mapper.VillageInformationMapper;
import com.demo.service.IVillageInformationService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VillageInformationServiceImpl extends ServiceImpl<VillageInformationMapper, VillageInformation> implements IVillageInformationService {

    @Override
    public SaResult getBasicDataByMap(String city, String county, String township, String hamlet, String site) {
        try {
            // 确定当前查询级别和下级查询级别
            String currentLevel = determineCurrentLevel(city, county, township, hamlet, site);
            String nextLevel = determineNextLevel(currentLevel);
            // 构建当前级别的汇总数据
            VillageInformation.HierarchicalData hierarchicalData = buildHierarchicalData(
                    city, county, township, hamlet, site, currentLevel, nextLevel);

            // 构建响应结构
            VillageInformation.HierarchicalDataResponse response = new VillageInformation.HierarchicalDataResponse();
            response.setData(hierarchicalData);

            return SaResult.data(response);

        } catch (Exception e) {
            log.error("查询分层级基础数据失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定区域的所有基础数据汇总（动态级别）
     * @param city 城市名称
     * @param county 区县名称（可选）
     * @param township 镇名称（可选）
     * @param hamlet 村名称（可选）
     * @param site 点位名称（可选，点位查询走村级逻辑）
     * @return 该区域所有数据的汇总统计
     */
    @Override
    public SaResult getRegionTotalSummary(String city, String county, String township, String hamlet, String site) {
        try {
            if (StringUtils.isBlank(city)) {
                return SaResult.error("城市名称不能为空");
            }

            // 确定当前查询级别
            String currentLevel = determineCurrentLevel(city, county, township, hamlet, site);

            // 特殊处理：点位查询走村级逻辑
            String actualLevel = currentLevel;
            String actualRegionName;
            if ("site".equals(currentLevel)) {
                actualLevel = "hamlet";
                actualRegionName = hamlet; // 使用村名作为区域名称
            } else {
                actualRegionName = getCurrentLevelName(city, county, township, hamlet, site, currentLevel);
            }

            // 构建查询条件（点位查询时查询整个村的数据）
            QueryWrapper<VillageInformation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("city", city);
            queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
            queryWrapper.eq(StringUtils.isNotBlank(township), "township", township);
            queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
            // 点位查询时不添加site条件，查询整个村的数据

            List<VillageInformation> allRegionData = baseMapper.selectList(queryWrapper);

            if (allRegionData.isEmpty()) {
                return SaResult.error("未找到 " + actualRegionName + " 的相关数据");
            }

            // 构建区域汇总数据
            VillageInformation.RegionTotalSummary summary = buildRegionTotalSummary(
                    actualRegionName, actualLevel, allRegionData);

            return SaResult.data(summary);

        } catch (Exception e) {
            log.error("查询区域基础数据汇总失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 确定当前查询级别
     */
    private String determineCurrentLevel(String city, String county, String township, String hamlet, String site) {
        if (StringUtils.isNotBlank(site)) {
            return "site";
        } else if (StringUtils.isNotBlank(hamlet)) {
            return "hamlet";
        } else if (StringUtils.isNotBlank(township)) {
            return "township";
        } else if (StringUtils.isNotBlank(county)) {
            return "county";
        } else {
            return "city";
        }
    }

    /**
     * 确定下级查询级别
     */
    private String determineNextLevel(String currentLevel) {
        return switch (currentLevel) {
            case "city" -> "county";
            case "county" -> "township";
            case "township" -> "hamlet";
            case "hamlet" ->
                // 村级查询不再查找点位，返回null
                    null;
            case "site" ->
                // 点位查询走村级查询逻辑，返回null
                    null;
            default -> null;
        };
    }

    /**
     * 构建分层级数据
     */
    private VillageInformation.HierarchicalData buildHierarchicalData(String city, String county,
            String township, String hamlet, String site, String currentLevel, String nextLevel) {

        VillageInformation.HierarchicalData data = new VillageInformation.HierarchicalData();

        // 特殊处理：点位查询走村级查询逻辑
        if ("site".equals(currentLevel)) {
            // 点位查询时，构建该点位所在村的汇总数据
            buildCurrentLevelSummary(data, city, county, township, hamlet, null, "hamlet");
            // 点位查询也返回该村的详细信息，和村级查询一样
            List<VillageInformation.RegionPoint> points = buildVillageDetailPoints(
                    city, county, township, hamlet);
            data.setPoint(points);
        } else {
            // 构建当前级别的汇总数据
            buildCurrentLevelSummary(data, city, county, township, hamlet, site, currentLevel);

            // 构建下级区域数据列表
            if (nextLevel != null) {
                List<VillageInformation.RegionPoint> points = buildNextLevelPoints(
                        city, county, township, hamlet, site, nextLevel);
                data.setPoint(points);
            } else {
                // 特殊处理：村级查询返回该村自己的详细信息
                if ("hamlet".equals(currentLevel)) {
                    List<VillageInformation.RegionPoint> points = buildVillageDetailPoints(
                            city, county, township, hamlet);
                    data.setPoint(points);
                } else {
                    // 其他情况返回空数组
                    data.setPoint(new ArrayList<>());
                }
            }
        }

        return data;
    }

    /**
     * 构建当前级别的汇总数据
     */
    private void buildCurrentLevelSummary(VillageInformation.HierarchicalData data, String city,
            String county, String township, String hamlet, String site, String currentLevel) {

        // 构建查询条件 - 查询当前级别及其所有下级数据
        QueryWrapper<VillageInformation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(city), "city", city);
        queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
        queryWrapper.eq(StringUtils.isNotBlank(township), "township", township);
        queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
        queryWrapper.eq(StringUtils.isNotBlank(site), "site", site);

        List<VillageInformation> allData = baseMapper.selectList(queryWrapper);

        if (!allData.isEmpty()) {
            // 设置当前级别名称
            String currentName = getCurrentLevelName(city, county, township, hamlet, site, currentLevel);
            data.setName(currentName);

            // 汇总统计数据
            Integer totalVehicles = allData.stream().mapToInt(v -> v.getVehicles() != null ? v.getVehicles() : 0).sum();
            Integer totalDrivers = allData.stream().mapToInt(v -> v.getDrivers() != null ? v.getDrivers() : 0).sum();
            Integer totalTransportCompanies = allData.stream().mapToInt(v -> v.getTransportCompanies() != null ? v.getTransportCompanies() : 0).sum();

            data.setTotalVehicles(totalVehicles);
            data.setTotalDrivers(totalDrivers);
            data.setTotalTransportCompanies(totalTransportCompanies);

            // 汇总面积和人口（可选）
            Double totalArea = allData.stream().mapToDouble(v -> parseAreaValue(v.getAreaOfJurisdiction())).sum();
            String totalPopulation = calculateTotalPopulation(allData);

            data.setArea(String.format("%.2f km²", totalArea));
            data.setPopulation(totalPopulation);
        }
    }

    /**
     * 构建下级区域数据列表
     */
    private List<VillageInformation.RegionPoint> buildNextLevelPoints(String city, String county,
            String township, String hamlet, String site, String nextLevel) {

        List<VillageInformation.RegionPoint> points = new ArrayList<>();

        // 构建基础查询条件
        QueryWrapper<VillageInformation> baseQuery = new QueryWrapper<>();
        baseQuery.eq(StringUtils.isNotBlank(city), "city", city);
        baseQuery.eq(StringUtils.isNotBlank(county), "county", county);
        baseQuery.eq(StringUtils.isNotBlank(township), "township", township);
        baseQuery.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
        baseQuery.eq(StringUtils.isNotBlank(site), "site", site);

        // 根据下级级别进行分组查询
        List<VillageInformation> allData = baseMapper.selectList(baseQuery);
        Map<String, List<VillageInformation>> groupedData = groupDataByLevel(allData, nextLevel);

        // 为每个下级区域构建数据
        for (Map.Entry<String, List<VillageInformation>> entry : groupedData.entrySet()) {
            String regionName = entry.getKey();
            List<VillageInformation> regionData = entry.getValue();

            if (StringUtils.isNotBlank(regionName) && !regionData.isEmpty()) {
                VillageInformation.RegionPoint point = buildRegionPoint(regionName, regionData);
                points.add(point);
            }
        }

        return points;
    }

    /**
     * 根据级别对数据进行分组
     */
    private Map<String, List<VillageInformation>> groupDataByLevel(List<VillageInformation> data, String level) {
        switch (level) {
            case "county":
                return data.stream()
                        .filter(v -> v.getCounty() != null && !v.getCounty().trim().isEmpty())
                        .collect(Collectors.groupingBy(VillageInformation::getCounty));
            case "township":
                return data.stream()
                        .filter(v -> v.getTownship() != null && !v.getTownship().trim().isEmpty())
                        .collect(Collectors.groupingBy(VillageInformation::getTownship));
            case "hamlet":
                return data.stream()
                        .filter(v -> v.getHamlet() != null && !v.getHamlet().trim().isEmpty())
                        .collect(Collectors.groupingBy(VillageInformation::getHamlet));
            case "site":
                return data.stream()
                        .filter(v -> v.getSite() != null && !v.getSite().trim().isEmpty())
                        .collect(Collectors.groupingBy(VillageInformation::getSite));
            default:
                return Map.of();
        }
    }

    /**
     * 构建区域点位数据
     */
    private VillageInformation.RegionPoint buildRegionPoint(String regionName, List<VillageInformation> regionData) {
        VillageInformation.RegionPoint point = new VillageInformation.RegionPoint();

        point.setName(regionName);

        // 汇总统计数据
        Integer vehicles = regionData.stream().mapToInt(v -> v.getVehicles() != null ? v.getVehicles() : 0).sum();
        Integer drivers = regionData.stream().mapToInt(v -> v.getDrivers() != null ? v.getDrivers() : 0).sum();
        Integer transportCompanies = regionData.stream().mapToInt(v -> v.getTransportCompanies() != null ? v.getTransportCompanies() : 0).sum();
        Integer nonMotorVehicles = regionData.stream().mapToInt(v -> v.getNonMotorVehicles() != null ? v.getNonMotorVehicles() : 0).sum();
        Integer keyFreightCompanies = regionData.stream().mapToInt(v -> v.getKeyFreightCompanies() != null ? v.getKeyFreightCompanies() : 0).sum();

        point.setVehicles(vehicles);
        point.setDrivers(drivers);
        point.setTransportCompanies(transportCompanies);
        point.setNonMotorVehicles(nonMotorVehicles);
        point.setKeyFreightCompanies(keyFreightCompanies);

        // 汇总面积和人口
        Double totalArea = regionData.stream().mapToDouble(v -> parseAreaValue(v.getAreaOfJurisdiction())).sum();
        String totalPopulation = calculateTotalPopulation(regionData);

        point.setArea(String.format("%.2f km²", totalArea));
        point.setPopulation(totalPopulation);

        // 构建道路信息
        VillageInformation.RoadInfo roads = buildRoadInfo(regionData);
        point.setRoads(roads);

        return point;
    }

    /**
     * 构建道路信息
     */
    private VillageInformation.RoadInfo buildRoadInfo(List<VillageInformation> regionData) {
        VillageInformation.RoadInfo roads = new VillageInformation.RoadInfo();

        Integer national = regionData.stream().mapToInt(v -> v.getNationalRoads() != null ? v.getNationalRoads() : 0).sum();
        Integer provincial = regionData.stream().mapToInt(v -> v.getProvincialRoads() != null ? v.getProvincialRoads() : 0).sum();
        Integer county = regionData.stream().mapToInt(v -> v.getCountyRoads() != null ? v.getCountyRoads() : 0).sum();
        Integer township = regionData.stream().mapToInt(v -> v.getTownshipRoads() != null ? v.getTownshipRoads() : 0).sum();
        Integer village = regionData.stream().mapToInt(v -> v.getVillageRoads() != null ? v.getVillageRoads() : 0).sum();
        Integer external = regionData.stream().mapToInt(v -> v.getExternalRoads() != null ? v.getExternalRoads() : 0).sum();

        roads.setNational(national);
        roads.setProvincial(provincial);
        roads.setCounty(county);
        roads.setTownship(township);
        roads.setVillage(village);
        roads.setExternal(external);

        return roads;
    }

    /**
     * 获取当前级别名称
     */
    private String getCurrentLevelName(String city, String county, String township, String hamlet, String site, String currentLevel) {
        return switch (currentLevel) {
            case "city" -> city;
            case "county" -> county;
            case "township" -> township;
            case "hamlet" -> hamlet;
            case "site" -> site;
            default -> "未知区域";
        };
    }

    /**
     * 解析面积值（处理字符串格式）
     */
    private double parseAreaValue(String areaStr) {
        if (areaStr == null || areaStr.trim().isEmpty()) {
            return 0.0;
        }

        try {
            // 提取数字部分，支持格式如 "1123.45 km²" 或 "1123.45"
            String numStr = areaStr.replaceAll("[^0-9.]", "");
            if (!numStr.isEmpty()) {
                return Double.parseDouble(numStr);
            }
        } catch (NumberFormatException e) {
            log.warn("解析面积数据失败: {}", areaStr);
        }

        return 0.0;
    }

    /**
     * 构建村级详细信息点位
     */
    private List<VillageInformation.RegionPoint> buildVillageDetailPoints(String city, String county, String township, String hamlet) {
        List<VillageInformation.RegionPoint> points = new ArrayList<>();

        // 查询该村的所有数据
        QueryWrapper<VillageInformation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(city), "city", city);
        queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
        queryWrapper.eq(StringUtils.isNotBlank(township), "township", township);
        queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);

        List<VillageInformation> villageData = baseMapper.selectList(queryWrapper);

        if (!villageData.isEmpty()) {
            // 创建该村的详细信息点位
            VillageInformation.RegionPoint point = new VillageInformation.RegionPoint();
            point.setName(hamlet); // 使用村名

            // 汇总该村的所有数据
            Integer vehicles = villageData.stream().mapToInt(v -> v.getVehicles() != null ? v.getVehicles() : 0).sum();
            Integer drivers = villageData.stream().mapToInt(v -> v.getDrivers() != null ? v.getDrivers() : 0).sum();
            Integer transportCompanies = villageData.stream().mapToInt(v -> v.getTransportCompanies() != null ? v.getTransportCompanies() : 0).sum();
            Integer nonMotorVehicles = villageData.stream().mapToInt(v -> v.getNonMotorVehicles() != null ? v.getNonMotorVehicles() : 0).sum();
            Integer keyFreightCompanies = villageData.stream().mapToInt(v -> v.getKeyFreightCompanies() != null ? v.getKeyFreightCompanies() : 0).sum();

            point.setVehicles(vehicles);
            point.setDrivers(drivers);
            point.setTransportCompanies(transportCompanies);
            point.setNonMotorVehicles(nonMotorVehicles);
            point.setKeyFreightCompanies(keyFreightCompanies);

            // 汇总面积和人口
            Double totalArea = villageData.stream().mapToDouble(v -> parseAreaValue(v.getAreaOfJurisdiction())).sum();
            String totalPopulation = calculateTotalPopulation(villageData);

            point.setArea(String.format("%.2f km²", totalArea));
            point.setPopulation(totalPopulation);

            // 构建道路信息
            VillageInformation.RoadInfo roads = buildRoadInfo(villageData);
            point.setRoads(roads);

            points.add(point);
        }

        return points;
    }

    /**
     * 构建区域基础数据汇总（通用）
     */
    private VillageInformation.RegionTotalSummary buildRegionTotalSummary(String regionName, String currentLevel, List<VillageInformation> allRegionData) {
        VillageInformation.RegionTotalSummary summary = new VillageInformation.RegionTotalSummary();
        VillageInformation.RegionSummaryData regionData = new VillageInformation.RegionSummaryData();

        // 设置区域基本信息
        regionData.setRegionName(regionName);
        regionData.setRegionLevel(currentLevel);

        // 汇总所有统计数据
        Integer totalVehicles = allRegionData.stream().mapToInt(v -> v.getVehicles() != null ? v.getVehicles() : 0).sum();
        Integer totalDrivers = allRegionData.stream().mapToInt(v -> v.getDrivers() != null ? v.getDrivers() : 0).sum();
        Integer totalTransportCompanies = allRegionData.stream().mapToInt(v -> v.getTransportCompanies() != null ? v.getTransportCompanies() : 0).sum();
        Integer totalNonMotorVehicles = allRegionData.stream().mapToInt(v -> v.getNonMotorVehicles() != null ? v.getNonMotorVehicles() : 0).sum();
        Integer totalKeyFreightCompanies = allRegionData.stream().mapToInt(v -> v.getKeyFreightCompanies() != null ? v.getKeyFreightCompanies() : 0).sum();

        regionData.setTotalVehicles(totalVehicles);
        regionData.setTotalDrivers(totalDrivers);
        regionData.setTotalTransportCompanies(totalTransportCompanies);
        regionData.setTotalNonMotorVehicles(totalNonMotorVehicles);
        regionData.setTotalKeyFreightCompanies(totalKeyFreightCompanies);

        // 汇总面积和人口
        Double totalArea = allRegionData.stream().mapToDouble(v -> parseAreaValue(v.getAreaOfJurisdiction())).sum();
        String totalPopulation = calculateTotalPopulation(allRegionData);

        regionData.setTotalArea(String.format("%.2f km²", totalArea));
        regionData.setTotalPopulation(totalPopulation);

        // 汇总道路信息
        VillageInformation.RoadInfo totalRoads = buildRoadInfo(allRegionData);
        regionData.setTotalRoads(totalRoads);

        // 统计下级区域信息
        String nextLevel = determineNextLevel(currentLevel);
        if (nextLevel != null) {
            long subRegionCount = getSubRegionCount(allRegionData, nextLevel);
            String subRegionType = getSubRegionTypeName(nextLevel);

            regionData.setSubRegionCount((int) subRegionCount);
            regionData.setSubRegionType(subRegionType);
        } else {
            regionData.setSubRegionCount(0);
            regionData.setSubRegionType("无下级");
        }

        // 记录总数
        regionData.setRecordCount(allRegionData.size());

        summary.setData(regionData);
        return summary;
    }

    /**
     * 获取下级区域数量
     */
    private long getSubRegionCount(List<VillageInformation> data, String level) {
        return switch (level) {
            case "county" ->
                    data.stream().map(VillageInformation::getCounty).filter(StringUtils::isNotBlank).distinct().count();
            case "township" ->
                    data.stream().map(VillageInformation::getTownship).filter(StringUtils::isNotBlank).distinct().count();
            case "hamlet" ->
                    data.stream().map(VillageInformation::getHamlet).filter(StringUtils::isNotBlank).distinct().count();
            case "site" ->
                    data.stream().map(VillageInformation::getSite).filter(StringUtils::isNotBlank).distinct().count();
            default -> 0;
        };
    }

    /**
     * 获取下级区域类型名称
     */
    private String getSubRegionTypeName(String level) {
        switch (level) {
            case "county":
                return "区县";
            case "township":
                return "镇";
            case "hamlet":
                return "村";
            case "site":
                return "点位";
            default:
                return "未知";
        }
    }

    /**
     * 计算总人口（处理字符串格式）
     */
    private String calculateTotalPopulation(List<VillageInformation> data) {
        // 如果人口字段是字符串格式（如"89.5万人"），需要特殊处理
        // 这里简化处理，实际可能需要解析字符串中的数字
        long totalCount = 0;
        for (VillageInformation item : data) {
            if (item.getRegisteredPopulation() != null) {
                String population = item.getRegisteredPopulation();
                // 简单处理：如果包含"万"，提取数字并转换
                if (population.contains("万")) {
                    try {
                        String numStr = population.replaceAll("[^0-9.]", "");
                        if (!numStr.isEmpty()) {
                            double num = Double.parseDouble(numStr);
                            totalCount += (long)(num * 10000);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("解析人口数据失败: {}", population);
                    }
                } else {
                    // 尝试直接解析数字
                    try {
                        String numStr = population.replaceAll("[^0-9]", "");
                        if (!numStr.isEmpty()) {
                            totalCount += Long.parseLong(numStr);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("解析人口数据失败: {}", population);
                    }
                }
            }
        }

        // 格式化返回结果
        if (totalCount >= 10000) {
            double wan = totalCount / 10000.0;
            return String.format("%.1f万人", wan);
        } else {
            return totalCount + "人";
        }
    }
}
