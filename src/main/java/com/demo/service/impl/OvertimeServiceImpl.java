package com.demo.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.demo.entity.Device;
import com.demo.entity.Overtime;
import com.demo.entity.Users;
import com.demo.mapper.OvertimeMapper;
import com.demo.mapper.UsersMapper;
import com.demo.service.OvertimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * 加班记录Service实现类
 */
@Slf4j
@Service
public class OvertimeServiceImpl implements OvertimeService {
    
    @Autowired
    private OvertimeMapper overtimeMapper;
    
    @Autowired
    private UsersMapper usersMapper;
    
    @Override
    @Transactional
    public Overtime recordOvertime(Integer userId, String userName, Device device, LocalDateTime time) {
        // 查询用户当天是否已有加班记录
        LocalDate today = time.toLocalDate();
        List<Overtime> todayOvertimes = getUserOvertimeByDate(userId, today);
        Date date = Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
        if (todayOvertimes != null && !todayOvertimes.isEmpty()) {
            // 已有加班记录，检查最近的一条
            Overtime lastOvertime = todayOvertimes.get(0);
            //计算当前时间是否在最近一条加班时间内
            if (lastOvertime.getEndTime() != null && lastOvertime.getEndTime().after(date)) {
                lastOvertime.setEndTime(Date.from(time.plusMinutes(1).atZone(ZoneId.systemDefault()).toInstant())); // 默认加班1分钟
                lastOvertime.setDuration((int) DateUtil.between(lastOvertime.getStartTime(), lastOvertime.getEndTime(), DateUnit.SECOND));
                overtimeMapper.updateById(lastOvertime);
                log.info("更新用户[{}]加班记录", userName);
                return lastOvertime;
            }
//             计算与上次打卡的时间差（秒）
//            long secondsDiff = DateUtil.between(lastOvertime.getStartTime(), date, DateUnit.SECOND);
//            long secondsDiff = ChronoUnit.SECONDS.between(lastOvertime.getStartTime(), time);
//            if (secondsDiff < 60) {
//                // 如果时间差小于1分钟，更新记录
//                lastOvertime.setEndTime(date);
//                // 计算应该增加的时长：60-(本次打卡时间-上次打卡时间)
//                int additionalSeconds = (int) (60 - secondsDiff);
//                // 更新加班时长
//                lastOvertime.setDuration(lastOvertime.getDuration() + additionalSeconds);
//                overtimeMapper.updateById(lastOvertime);
//                log.info("更新用户[{}]加班记录，时长:{}分钟", userName, lastOvertime.getDuration() / 60.0);
//                return lastOvertime;
//            }
        }
        
        // 创建新的加班记录（默认1分钟）
        Overtime overtime = new Overtime();
        overtime.setUserId(userId);
        overtime.setUserName(userName);
        overtime.setStartTime(date);
        overtime.setEndTime(Date.from(time.plusMinutes(1).atZone(ZoneId.systemDefault()).toInstant())); // 默认加班1分钟
        overtime.setDuration(60); // 默认60秒
        overtime.setCity(device.getCity());
        overtime.setCounty(device.getCounty());
        overtime.setTownship(device.getTownship());
        overtime.setHamlet(device.getHamlet());
        overtime.setSite(device.getSite());
        overtime.setEquipmentNumber(device.getEquipmentNumber());

        overtimeMapper.insert(overtime);
        log.info("记录用户[{}]加班，时长:1分钟", userName);
        
        return overtime;
    }
    
    @Override
    @Transactional
    public Overtime recordOvertimeForFulltimeStaff(Device device, LocalDateTime time) {
        // 查找该地点的专职劝导员
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dept_name", "专职")
                .eq("city", device.getCity())
                .eq("county", device.getCounty())
                .eq("township", device.getTownship())
                .eq("hamlet", device.getHamlet())
                .eq("site", device.getSite())
                .eq("state", 0) // 正常状态
                .orderByAsc("user_id") // 按ID排序，取第一个
                .last("LIMIT 1");
        
        Users user = usersMapper.selectOne(queryWrapper);
        if (user == null) {
                return null;
        }
        // 记录专职劝导员加班
        log.info("记录专职劝导员[{}]加班", user.getName());
        return recordOvertime(user.getUserId(), user.getName(), device, time);
    }
    
    @Override
    public List<Overtime> getUserOvertimeByDate(Integer userId, LocalDate date) {
        return overtimeMapper.getUserOvertimeByDate(userId, date);
    }
    
    @Override
    public List<Overtime> getUserOvertimeByTimeRange(Integer userId, LocalDateTime startTime, LocalDateTime endTime) {
        return overtimeMapper.getUserOvertimeByTimeRange(userId, startTime, endTime);
    }
    
    @Override
    public List<Overtime> getAllOvertimesByDateRange(LocalDate startDate, LocalDate endDate) {
        return overtimeMapper.getAllOvertimesByDateRange(startDate, endDate);
    }
    
    @Override
    public List<Overtime> getOvertimesByAreaAndDateRange(
            String city, String county, String township, String hamlet, String site, 
            LocalDate startDate, LocalDate endDate) {
        return overtimeMapper.getOvertimesByAreaAndDateRange(
                city, county, township, hamlet, site, startDate, endDate);
    }
} 