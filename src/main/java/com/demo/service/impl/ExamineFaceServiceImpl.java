package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.DTO.ExamineFaceDTO;
import com.demo.entity.Device;
import com.demo.entity.ExamineFace;
import com.demo.entity.Face;
import com.demo.entity.Users;
import com.demo.mapper.DeviceMapper;
import com.demo.mapper.ExamineFaceMapper;
import com.demo.mapper.FaceMapper;
import com.demo.mapper.UsersMapper;
import com.demo.service.ExamineFaceService;
import com.demo.utils.MinioUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

import static java.util.concurrent.TimeUnit.HOURS;

@Service
public class ExamineFaceServiceImpl extends ServiceImpl<ExamineFaceMapper, ExamineFace> implements ExamineFaceService {
    @Autowired
    private ExamineFaceMapper examineFaceMapper;
    @Autowired
    DeviceMapper deviceMapper;
    @Autowired
    FaceMapper faceMapper;
    @Autowired
    MinioUtil minioUtil;
    @Autowired
    UsersMapper usersMapper;

    @Override
    public SaResult submitExamineFace(String equipmentNumber, MultipartFile[] image) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_number", equipmentNumber);
        List<Device> devices = deviceMapper.selectList(queryWrapper);
        if (devices == null) {
            return SaResult.error("设备不存在");
        }
        Device device = devices.get(0);
        String formatDate = DateUtil.formatDate(new Date());    //日期
        String address = buildAddress(device);    //构建地址
        String folderName ="审核人脸" +"/"+formatDate + "/" + address;    //构建文件夹名
        for (MultipartFile file : image) {
            if (file.isEmpty()) {
                return SaResult.error("图片不能为空");
            }
            String fileName = file.getOriginalFilename();
            String upload = minioUtil.uploadFile(file, fileName,folderName);
            ExamineFace examineFace = new ExamineFace();
            examineFace.setEquipmentnumber(equipmentNumber);
            examineFace.setExamineUrl(upload);
            examineFace.setExamine("未审核");
            examineFace.setCity(device.getCity());
            examineFace.setCounty(device.getCounty());
            examineFace.setTownship(device.getTownship());
            examineFace.setHamlet(device.getHamlet());
            examineFace.setSite(device.getSite());
            examineFaceMapper.insert(examineFace);
        }
        return SaResult.ok("提交审核成功");
    }
    /**
     * 构建地址
     *
     * @param device 包含地址信息的设备对象
     * @return 构建好的地址字符串
     */
    private String buildAddress(Device device) {
        StringBuilder address = new StringBuilder();
        if (StringUtils.isNotBlank(device.getCity())) {
            address.append(device.getCity());
        }
        if (StringUtils.isNotBlank(device.getCounty())) {
            address.append(device.getCounty());
        }
        if (StringUtils.isNotBlank(device.getTownship())) {
            address.append(device.getTownship());
        }
        if (StringUtils.isNotBlank(device.getHamlet())) {
            address.append(device.getHamlet());
        }
        if (StringUtils.isNotBlank(device.getSite())) {
            address.append(device.getSite());
        }
        return address.toString();
    }
    @Override
    public SaResult selectExamineFace(String city, String county, String township, String hamlet, String site, String examine, Integer pageSize, Integer curPage) {
        IPage<ExamineFace> page = new Page<>();
        page.setCurrent(curPage);
        page.setSize(pageSize);
        QueryWrapper<ExamineFace> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(examine),"examine", examine);
        queryWrapper.eq(StringUtils.isNotEmpty(city),"city", city);
        queryWrapper.eq(StringUtils.isNotEmpty(county),"county", county);
        queryWrapper.eq(StringUtils.isNotEmpty(township),"township", township);
        queryWrapper.eq(StringUtils.isNotEmpty(hamlet),"hamlet", hamlet);
        queryWrapper.eq(StringUtils.isNotEmpty(site),"site", site);
        queryWrapper.orderByDesc("create_time");
        IPage<ExamineFace> examineFaceIPage = examineFaceMapper.selectPage(page, queryWrapper);
        List<ExamineFace> records = examineFaceIPage.getRecords();
        for (ExamineFace record : records) {
            String examineUrl = record.getExamineUrl();
            if (examineUrl != null && !examineUrl.isEmpty()) {
                String expireUrl = minioUtil.getExpireFileUrl(examineUrl, 1, HOURS);
                record.setExamineUrl(expireUrl);
            }
        }
        return SaResult.data(examineFaceIPage);
    }

    @Override
    @Transactional
    public SaResult examineFace(ExamineFaceDTO examineFaceDTO) {
        if (!examineFaceDTO.getIsExamine()) {
            ExamineFace examineFace = new ExamineFace();
            examineFace.setId(examineFaceDTO.getId());
            examineFace.setExamine("未通过");
            examineFace.setReviewer(examineFaceDTO.getReviewer());
            examineFace.setReviewerId(examineFaceDTO.getReviewerId());
            examineFace.setReviewerTime(new Date());
            examineFaceMapper.updateById(examineFace);
            return SaResult.ok("审核成功");
        }
        QueryWrapper<ExamineFace> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", examineFaceDTO.getId());
        ExamineFace examineFace = examineFaceMapper.selectOne(queryWrapper);
        examineFace.setExamine("通过");
        examineFace.setReviewer(examineFaceDTO.getReviewer());
        examineFace.setReviewerId(examineFaceDTO.getReviewerId());
        examineFace.setBindingUserid(examineFaceDTO.getUserId());
        examineFace.setBindingUsername(examineFaceDTO.getUserName());
        examineFace.setReviewerTime(new Date());
        examineFaceMapper.updateById(examineFace);
        String examineUrl = examineFace.getExamineUrl();
        Face face = new Face();
        face.setFaceUrl(examineUrl);
        face.setUserId(examineFaceDTO.getUserId());
        face.setUserName(examineFaceDTO.getUserName());
        faceMapper.insert(face);
        return SaResult.ok("审核成功");
    }

    @Override
    public SaResult getUsers(String city, String county, String township, String hamlet, String site) {
        List<Users> users = usersMapper.selectProselytizer(city, county, township, hamlet, site);
        if (users.isEmpty()) {
            return SaResult.empty();
        } else {
            return SaResult.data(users);
        }
    }
}
