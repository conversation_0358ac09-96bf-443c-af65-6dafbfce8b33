package com.demo.service.impl;

import com.demo.service.AttendanceCacheService;
import com.demo.service.IllegalRecordsService;
import com.demo.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.concurrent.TimeUnit;

/**
 * 考勤缓存服务实现
 */
@Slf4j
@Service
public class AttendanceCacheServiceImpl implements AttendanceCacheService {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IllegalRecordsService illegalRecordsService;

    /**
     * 缓存键前缀
     */
    private static final String CACHE_PREFIX = "attendance:stats:";

    /**
     * 缓存统计信息键
     */
    private static final String CACHE_STATS_KEY = "attendance:cache:stats";

    /**
     * 预热间隔时间（毫秒）
     */
    private static final long WARMUP_INTERVAL = 10;



    @Override
    public void cleanExpiredCache() {
        log.info("开始清理过期缓存");
        
        try {
            // 获取所有考勤缓存键
            String pattern = CACHE_PREFIX + "*";
            // 这里需要根据实际的Redis工具类实现来清理过期缓存
             redisUtils.deleteByPattern(pattern);
            
            log.info("过期缓存清理完成");
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
        }
    }

    @Override
    public String getCacheStats() {
        try {
            String stats = redisUtils.get(CACHE_STATS_KEY);
            return stats != null ? stats : "暂无缓存统计信息";
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return "获取缓存统计信息失败: " + e.getMessage();
        }
    }

    @Override
    public void refreshCache(LocalDate date, String city, String county, String township, String hamlet, String site) {
        try {
            log.info("刷新缓存，日期: {}, 地区: {}-{}-{}-{}-{}", date, city, county, township, hamlet, site);
            
            // 先删除现有缓存
            String cacheKey = buildCacheKey(date, city, county, township, hamlet, site);
            redisUtils.delete(cacheKey);
            
            // 重新生成缓存
            illegalRecordsService.getAttendanceAndWorkTimeByday(city, county, township, hamlet, site, date);
            
            log.info("缓存刷新完成");
        } catch (Exception e) {
            log.error("刷新缓存失败", e);
        }
    }



    /**
     * 更新缓存统计信息
     */
    public void updateCacheStats(String operation, boolean success) {
        try {
            String stats = String.format(
                "最后操作: %s, 时间: %s, 状态: %s",
                operation, LocalDate.now(), success ? "成功" : "失败"
            );
            redisUtils.setEx(CACHE_STATS_KEY, stats, 24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("更新缓存统计信息失败", e);
        }
    }

    private String buildCacheKey(LocalDate date, String city, String county, String township, String hamlet, String site) {
        return String.format("%s%s:%s:%s:%s:%s:%s",
                CACHE_PREFIX, date,
                city != null ? city : "null",
                county != null ? county : "null",
                township != null ? township : "null",
                hamlet != null ? hamlet : "null",
                site != null ? site : "null");
    }
}
