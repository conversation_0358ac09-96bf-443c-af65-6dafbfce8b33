package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.Device;
import com.demo.entity.Face;
import com.demo.entity.Users;
import com.demo.mapper.DeviceMapper;
import com.demo.mapper.FaceMapper;
import com.demo.mapper.UsersMapper;
import com.demo.service.FaceService;
import com.demo.utils.MinioUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static java.util.concurrent.TimeUnit.HOURS;

@Service
public class FaceServiceImpl extends ServiceImpl<FaceMapper, Face> implements FaceService {
    @Autowired
    FaceMapper faceMapper;
    @Autowired
    DeviceMapper deviceMapper;
    @Autowired
    UsersMapper usersMapper;
    @Autowired
    MinioUtil minioUtil;

    @Override
    public SaResult getPointFace(String equipmentNumber) {
        QueryWrapper<Device> queryWrapperDevice = new QueryWrapper<>();
        queryWrapperDevice.eq("equipment_number", equipmentNumber);
        List<Device> devices = deviceMapper.selectList(queryWrapperDevice);
        if (devices == null || devices.isEmpty()) {
            return SaResult.error("设备不存在");
        }
        Device device = devices.get(0);
        QueryWrapper<Users> queryWrapperUser = new QueryWrapper<>();
        queryWrapperUser.eq(StringUtils.isNotBlank(device.getCity()), "city", device.getCity());
        queryWrapperUser.eq(StringUtils.isNotBlank(device.getCounty()), "county", device.getCounty());
        queryWrapperUser.eq(StringUtils.isNotBlank(device.getTownship()), "township", device.getTownship());
        queryWrapperUser.eq(StringUtils.isNotBlank(device.getHamlet()), "hamlet", device.getHamlet());
        queryWrapperUser.eq(StringUtils.isNotBlank(device.getSite()), "site", device.getSite());
        List<Users> users = usersMapper.selectList(queryWrapperUser);
        if (users.isEmpty()) {
            return SaResult.error("该设备下没有用户");
        }
        //获取users下的ID并转换为List<Integer>
        List<Integer> userIds = users.stream()
                .map(Users::getUserId)
                .toList();
        QueryWrapper<Face> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("user_id", userIds);
        List<Face> faces = faceMapper.selectList(queryWrapper);
        for (Face face : faces) {
            String faceUrl = face.getFaceUrl();
            if (faceUrl != null && !faceUrl.isEmpty()) {
                String expireUrl = minioUtil.getExpireFileUrl(faceUrl, 1, HOURS);
                face.setFaceUrl(expireUrl);
            }
        }
        return SaResult.data(faces);
    }
}
