package com.demo.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.DTO.*;
import com.demo.entity.RoleUsers;
import com.demo.entity.TreeNode;
import com.demo.entity.Users;
import com.demo.entity.VO.AllThePersuadersVO;
import com.demo.mapper.RoleUsersMapper;
import com.demo.mapper.TreeNodeMapper;
import com.demo.mapper.UsersMapper;
import com.demo.service.UsersService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

@Service
@Slf4j
public class UsersServiceImpl extends ServiceImpl<UsersMapper, Users> implements UsersService {
    @Autowired
    UsersMapper usersMapper;
    @Autowired
    RoleUsersMapper roleUsersMapper;
    @Autowired
    TreeNodeMapper treeNodeMapper;

    @Override
    public Users selectOne(String phone) {
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("phone", phone);
        return usersMapper.selectOne(queryWrapper);
    }

    @Override
    public SaResult updateUser(UpdateUser users) throws InvocationTargetException, IllegalAccessException {
        String city = users.getCity();
        String county = users.getCounty();
        String township = users.getTownship();
        String hamlet = users.getHamlet();
        String site = users.getSite();
        // 检查并插入城市
        TreeNode cityNode = insertIfNotExists(city, 0,0);
        if (cityNode != null) {
            // 检查并插入县
            TreeNode countyNode = insertIfNotExists(county, cityNode.getId(),1);
            if (countyNode != null) {
                // 检查并插入镇
                TreeNode townshipNode = insertIfNotExists(township, countyNode.getId(), 2);
                if (townshipNode!=null){
                    // 检查并插入村
                    TreeNode hamletNode = insertIfNotExists(hamlet, townshipNode.getId(),3);
                    if (hamletNode!=null){
                        // 检查并插入详细地址
                        insertIfNotExists(site, hamletNode.getId(), 4);
                    }
                }
            }
        }
        Users user = new Users();
        BeanUtils.copyProperties(user, users);
        usersMapper.updateById(user);
        return SaResult.ok("修改成功");
    }

    @Override
    public SaResult getAllThePersuaders(IocationDTO iocationDTO) {
        List<String> roleList = StpUtil.getRoleList(); // 获取：当前账号的角色集合
        int loginIdAsInt = StpUtil.getLoginIdAsInt(); // 获取当前会话账号id, 并转换为int类型
        Users user = usersMapper.selectById(loginIdAsInt);
        // 根据用户角色和位置信息确定顶级节点
        boolean hasPermission = true;
        if (roleList.contains("city")) {
            if (StringUtils.isEmpty(user.getCounty())) {
                return SaResult.error("请先设置市");
            }
            iocationDTO.setCity(user.getCity());
            hasPermission = false;
        }
        if (roleList.contains("county") && hasPermission) {
            if (StringUtils.isEmpty(user.getTownship())) {
                return SaResult.error("请先设置县");
            }
            iocationDTO.setCity(user.getCity());
            iocationDTO.setCounty(user.getCounty());
            hasPermission = false;
        }
        if (roleList.contains("township") && hasPermission) {
            if (StringUtils.isEmpty(user.getHamlet())) {
                return SaResult.error("请先设置乡镇");
            }
            iocationDTO.setCity(user.getCity());
            iocationDTO.setCounty(user.getCounty());
            iocationDTO.setTownship(user.getTownship());
            hasPermission = false;
        }
        if (roleList.contains("hamlet") && hasPermission) {
            if (StringUtils.isEmpty(user.getSite())) {
                return SaResult.error("请先设置村");
            }
            iocationDTO.setCity(user.getCity());
            iocationDTO.setCounty(user.getCounty());
            iocationDTO.setTownship(user.getTownship());
            iocationDTO.setHamlet(user.getHamlet());
            hasPermission = false;
        }
        if (roleList.contains("site") && hasPermission) {
            if (StringUtils.isEmpty(user.getSite())) {
                return SaResult.error("请先设置点位");
            }
            iocationDTO.setCity(user.getCity());
            iocationDTO.setCounty(user.getCounty());
            iocationDTO.setTownship(user.getTownship());
            iocationDTO.setHamlet(user.getHamlet());
            iocationDTO.setSite(user.getSite());
            hasPermission = false;
        }
        if (hasPermission) {
            return SaResult.error("没有权限");
        }
        IPage<AllThePersuadersVO> page = new Page<>();
        page.setCurrent(iocationDTO.getCurPage());
        page.setSize(iocationDTO.getPageSize());
        IPage<AllThePersuadersVO> users = usersMapper.getAllThePersuaders(page, iocationDTO);
        return SaResult.data(users);
    }

    private TreeNode insertIfNotExists(String label, int parentId,int level) {
        if (label == null || label.isEmpty()) {
            return null; // 或者你可以选择抛出异常，或者返回一个默认值
        }
        QueryWrapper<TreeNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("label", label);
        queryWrapper.eq("parentId", parentId);
        TreeNode node = treeNodeMapper.selectOne(queryWrapper);
        if (node == null) {
            node = new TreeNode();
            node.setLabel(label);
            node.setLevel(level);
            node.setParentId(parentId);
            treeNodeMapper.insert(node);
        }
        return node;
    }

    @Override
    @Transactional // 添加事务注解
    public SaResult addUser(AddUser addUser) throws InvocationTargetException, IllegalAccessException {
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("phone", addUser.getPhone());
        Users users = usersMapper.selectOne(queryWrapper);
        if (users != null) {
            return SaResult.error("手机号已存在");
        }
        Users user = new Users();
        BeanUtils.copyProperties(user, addUser);
        //todo 后期确定密码加密
        String city = user.getCity();
        String county = user.getCounty();
        String township = user.getTownship();
        String hamlet = user.getHamlet();
        String site = user.getSite();
        // 检查并插入城市
        TreeNode cityNode = insertIfNotExists(city, 0,0);
        if (cityNode != null) {
            // 检查并插入县
            TreeNode countyNode = insertIfNotExists(county, cityNode.getId(),1);
            if (countyNode != null) {
                // 检查并插入镇
                TreeNode townshipNode = insertIfNotExists(township, countyNode.getId(), 2);
                if (townshipNode!=null){
                    // 检查并插入村
                    TreeNode hamletNode = insertIfNotExists(hamlet, townshipNode.getId(),3);
                    if (hamletNode!=null){
                        // 检查并插入详细地址
                        insertIfNotExists(site, hamletNode.getId(), 4);
                    }
                }
            }
        }
        int insert = usersMapper.insert(user);
        if (insert > 0) {
            log.info("增加用户{}成功：" + user.getName());
            Integer insertedUserId = user.getUserId();
            // 处理插入后的ID
            roleUsersMapper.insertRole(insertedUserId, addUser.getRole());
            log.info("用户{}添加角色{}成功", user.getName(), addUser.getRole());
        }
        return SaResult.ok("用户增加成功");
    }

    @Override
    @Transactional // 添加事务注解
    public String deleteUser(Integer id) {
        usersMapper.deleteById(id);
        StpUtil.kickout(id);
        QueryWrapper<RoleUsers> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", id);
        roleUsersMapper.delete(queryWrapper);
        StpUtil.kickout(id);
        return "删除成功";
    }

    @Override
    public String frozenUser(StateSwitching stateSwitching) {
        Integer state = stateSwitching.getState();
        Users user = new Users();
        user.setUserId(stateSwitching.getId());
        if (state == 1) {
            user.setState(1);
            usersMapper.updateById(user);
            StpUtil.kickout(stateSwitching.getId());
            return "冻结成功";
        } else {
            user.setState(0);
            usersMapper.updateById(user);
            StpUtil.kickout(stateSwitching.getId());
            return "解冻成功";
        }

    }

    @Override
    public SaResult selectUsers(SelectUsers selectUsers) {
        int offset = (selectUsers.getCurPage() - 1) * selectUsers.getPageSize();
        // 获取分页数据
        List<Users> users = usersMapper.selectUsers(offset, selectUsers.getPageSize(), selectUsers.getName(), selectUsers.getPhone(), selectUsers.getSex()
                , selectUsers.getCity(), selectUsers.getCounty(), selectUsers.getTownship(), selectUsers.getHamlet(), selectUsers.getSite(), selectUsers.getState());
        // 设置 total 条数
        long total = usersMapper.countUsers(selectUsers.getName(), selectUsers.getPhone(), selectUsers.getSex()
                , selectUsers.getCity(), selectUsers.getCounty(), selectUsers.getTownship(), selectUsers.getHamlet(), selectUsers.getSite(), selectUsers.getState());
        IPage<Users> rolePage = new Page<>();
        rolePage.setTotal(total);
        rolePage.setRecords(users);
        rolePage.setCurrent(selectUsers.getCurPage());
        rolePage.setSize(selectUsers.getPageSize());
        return SaResult.ok().setData(rolePage);
    }

    @Override
    @Transactional // 添加事务注解
    public String deleteUserList(List<Integer> ids) {
        usersMapper.deleteBatchIds(ids);
        QueryWrapper<RoleUsers> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("user_id", ids);
        roleUsersMapper.delete(queryWrapper);
        for (Integer id : ids) {
            StpUtil.kickout(id);
        }
        return "批量删除成功";
    }

    @Override
    public String frozenUserList(List<Integer> ids) {
        usersMapper.updatefrozenUserList(ids);
        for (Integer id : ids) {
            StpUtil.kickout(id);
        }
        return "批量冻结成功";
    }


}
