package com.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.DTO.ShiftGroupDTO;
import com.demo.entity.Shift;
import com.demo.entity.ShiftGroup;
import com.demo.mapper.ShiftGroupMapper;
import com.demo.mapper.ShiftGroupRelationMapper;
import com.demo.service.ShiftGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ShiftGroupServiceImpl extends ServiceImpl<ShiftGroupMapper, ShiftGroup>
        implements ShiftGroupService {
    @Autowired
    ShiftGroupRelationMapper shiftGroupRelationMapper;

    /**
     * 获取所有启用状态的班次组
     *
     * @return 启用状态的班次组列表
     */
    @Override
    public List<ShiftGroup> getActiveGroups() {
        return this.list(new LambdaQueryWrapper<ShiftGroup>()
                //.eq(ShiftGroup::getStatus, 1)  // 状态为启用
                .orderByAsc(ShiftGroup::getCreateTime));
    }

    /**
     * 创建新的班次组
     *
     * @param group 班次组信息
     * @return 创建后的班次组
     */
    @Override
    @Transactional
    public ShiftGroup createGroup(ShiftGroup group) {
        // 验证工作模式
        if (!validateWorkPattern(group.getWorkPattern())) {
            throw new RuntimeException("工作模式格式不正确");
        }
        // 设置默认值
        //group.setStatus(1);  // 默认启用
        group.setCreateTime(LocalDateTime.now());
        // 保存班次组
        this.save(group);
        return group;
    }

    /**
     * 更新班次组状态
     *
     * @param id     班次组ID
     * @param status 状态：1-启用，0-禁用
     * @return 是否更新成功
     */
    @Override
    @Transactional
    public boolean updateStatus(Integer id, Integer status) {
        return this.update(new LambdaUpdateWrapper<ShiftGroup>()
                .eq(ShiftGroup::getId, id)
                //.set(ShiftGroup::getStatus, status)
                .set(ShiftGroup::getUpdateTime, LocalDateTime.now()));
    }

    /**
     * 验证工作模式格式
     *
     * @param workPattern 工作模式字符串，如：1,1,0
     * @return 是否格式正确
     */
    @Override
    public boolean validateWorkPattern(String workPattern) {
        if (workPattern == null || workPattern.isEmpty()) {
            return false;
        }

        // 检查格式：只能包含0,1和逗号
        return workPattern.matches("^[0,1]+$") &&
                workPattern.split(",").length > 0;
    }

    @Override
    public List<Integer> getShiftIdsByGroupId(Integer groupId) {
        return shiftGroupRelationMapper.selectShiftIdsByGroupId(groupId);
    }

    @Override
    public IPage<ShiftGroupDTO> getActiveGroupsWithShifts(Page<ShiftGroup> page) {
        IPage<ShiftGroup> activeGroupsPage = this.page(page, new LambdaQueryWrapper<ShiftGroup>());
        List<ShiftGroupDTO> groupDTOs = activeGroupsPage.getRecords().stream().map(group -> {
            ShiftGroupDTO dto = new ShiftGroupDTO();
            BeanUtils.copyProperties(group, dto);
            // 获取班组下的班次
            List<Shift> shifts = shiftGroupRelationMapper.selectShiftsByGroupId(group.getId());
            dto.setShifts(shifts);
            return dto;
        }).collect(Collectors.toList());
        IPage<ShiftGroupDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), activeGroupsPage.getTotal());
        resultPage.setRecords(groupDTOs);
        return resultPage;
    }
} 