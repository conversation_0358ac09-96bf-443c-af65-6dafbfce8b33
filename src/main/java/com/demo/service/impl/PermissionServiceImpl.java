package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.Permission;
import com.demo.mapper.PermissionMapper;
import com.demo.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission> implements PermissionService {
    @Autowired
    PermissionMapper permissionMapper;

    @Override
    public Permission selectById(Integer permissionId) {
        return permissionMapper.selectById(permissionId);
    }

    @Override
    public SaResult selectPermission() {
        List<Permission> permissions = permissionMapper.selectList(new QueryWrapper<>());
        return SaResult.data(permissions);
    }
}
