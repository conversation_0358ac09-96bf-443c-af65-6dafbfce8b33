package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.DTO.*;
import com.demo.entity.Users;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

public interface UsersService extends IService<Users>{


    SaResult addUser(AddUser addUser) throws InvocationTargetException, IllegalAccessException;

    String deleteUser(Integer id);

    String frozenUser(StateSwitching stateSwitching);

    SaResult selectUsers(SelectUsers selectUsers);

    String deleteUserList(List<Integer> ids);

    String frozenUserList(List<Integer> ids);

    Users selectOne(String phone);

    SaResult updateUser(UpdateUser users) throws InvocationTargetException, IllegalAccessException;

    SaResult getAllThePersuaders(IocationDTO iocationDTO);


}
