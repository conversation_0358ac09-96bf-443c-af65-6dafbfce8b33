package com.demo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.UserShiftGroup;

import java.time.LocalDateTime;
import java.util.List;

public interface UserShiftGroupService extends IService<UserShiftGroup> {
    
    /**
     * 分配员工到班次组
     *
     * @param userId       员工ID
     * @param groupId      班次组ID
     * @param fixedShiftId 固定班次ID（如果是轮班制则为null）
     * @param startDate    生效日期
     * @param endDate
     */
    void assignUserToGroup(Integer userId, Integer groupId, Integer fixedShiftId, LocalDateTime startDate, LocalDateTime endDate) ;
    
    /**
     * 获取员工当前的班次组
     */
    UserShiftGroup getCurrentGroup(Integer userId, LocalDateTime date);
    
    /**
     * 获取班次组下的所有员工
     */
    List<Integer> getGroupUsers(Integer groupId);
    
    /**
     * 获取用户当前的班组
     */
    List<UserShiftGroup> getUserGroups(Integer userId, LocalDateTime date);

    void updateUserGroup(Integer userId, Integer groupId, Integer fixedShiftId, LocalDateTime startDate);

    /**
     * 获取所有需要自动排班的劝导员的用户ID
     * @return 劝导员用户ID列表
     */
    List<Integer> getAllCounselorsWithShiftGroup();
}