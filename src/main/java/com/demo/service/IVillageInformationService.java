package com.demo.service;



import cn.dev33.satoken.util.SaResult;
import com.demo.entity.VillageInformation;

import java.util.List;

/**
 * @Description: 区县数据
 * @Author: lee
 * @Date:  2025-02-27
 * @Version: V1.0
 */
public interface IVillageInformationService {
	/**
	 * 查询分层级基础数据
	 */
    SaResult getBasicDataByMap(String city, String county, String township, String hamlet, String site);

    /**
     * 获取指定区域的所有基础数据汇总（动态级别）
     */
    SaResult getRegionTotalSummary(String city, String county, String township, String hamlet, String site);
}
