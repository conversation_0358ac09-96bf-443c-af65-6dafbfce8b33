package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.DTO.AssignRole;
import com.demo.entity.DTO.RoleDTO;
import com.demo.entity.DTO.SelectRoles;
import com.demo.entity.Role;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RoleService extends IService<Role>{

    SaResult selectRoles(SelectRoles selectRoles);

    SaResult addRole(RoleDTO roleDTO) throws InvocationTargetException, IllegalAccessException;

    SaResult updateRole(RoleDTO roleDTO) throws InvocationTargetException, IllegalAccessException;

    SaResult selectPermissionList(Integer id);

    SaResult deleteById(Integer id);

    SaResult deleteByIds(List<Integer> ids);

    SaResult selectRoleList( );

    SaResult assignRole(AssignRole assignRole);
}
