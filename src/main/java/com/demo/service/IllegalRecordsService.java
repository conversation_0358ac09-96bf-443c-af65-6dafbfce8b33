package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.DTO.AssignACounselor;
import com.demo.entity.DTO.IllegalRecordsDTO;
import com.demo.entity.DTO.IocationDTO;
import com.demo.entity.DTO.SelectIllegalRecords;
import com.demo.entity.IllegalRecords;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IllegalRecordsService extends IService<IllegalRecords>{


    SaResult addIllegalRecords(IllegalRecords illegalRecords, MultipartFile[] files) throws  IOException;

    SaResult selectIllegalRecords(SelectIllegalRecords selectIllegalRecords);

    SaResult assignACounselor(AssignACounselor assignACounselor);

    SaResult selectProselytizer(IocationDTO ionationDTO);

    SaResult selectProselytizerList(IocationDTO ionationDTO);

    SaResult selectSentDown(String uuid);

    SaResult selectDetails(String uuid);

    SaResult todayStatistics();

    SaResult getViolationTrendsByWeek(Date startTime, Date endTime, String city, String county, String township, String hamlet, String site);

    SaResult getViolationTrendsByMonth(int year, int month, String city, String county, String township, String hamlet, String site);

    SaResult getViolationTrendsByYear(int year, String city, String county, String township, String hamlet, String site);

    SaResult getTopLocationsByYear(int year);

    SaResult getTopLocationsByMonth(int year, int month);

    SaResult getTopLocationsByDay(int year, int month, int day);

    SaResult getTopLocationsByWeek(int year, int week);

    SaResult getTopLocationsByLastSevenDays();

    SaResult getViolationCountsByDay(LocalDate date, String city, String county, String township, String hamlet, String site);

    SaResult getViolationTrendsByDay(LocalDate date, String city, String county, String township, String hamlet, String site);

    SaResult getViolationCountsByTimeRange(Date startTime, Date endTime, String city, String county, String township, String hamlet, String site);

    Map<String, Object> getWeeklyStatisticsForECharts();

    SaResult getViolationTypeAnalysis(String city, String county, String township, String hamlet, String site);

    SaResult topLocationsByDay(String city, String county, String township, String hamlet, String site, Date date);

    //SaResult getIllegalData();


    SaResult topLocationsByMonth(String city, String county, String township, String hamlet, String site, Date date);

    SaResult DayStatistics(String city, String county, String township, String hamlet, String site, Date date);

    SaResult MonthStatistics(String city, String county, String township, String hamlet, String site, Date date);

    SaResult getPointDetailDataByDay(String city, String county, String township, String hamlet, String site, int year, int month, int day);

    SaResult getPointDetailDataByMonth(String city, String county, String township, String hamlet, String site, int year, int month);

    SaResult getPointDetailDataByYear(String city, String county, String township, String hamlet, String site, int year);

    SaResult getViolationTypeStatistics(String city, String county, String township, String hamlet, String site, Date date);

    /**
     * 获取工作时间内劝导统计
     * 根据传入的地理层级返回下一级的汇总数据
     * 例如：传入市级，返回该市下所有区县的统计数据
     *
     * @param city 市
     * @param county 区县
     * @param township 镇街道
     * @param hamlet 社区/村
     * @param site 点位
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按地理层级汇总的统计数据
     */
    SaResult getPersuasionStatsByWorkTime(String city, String county, String township, 
        String hamlet, String site, LocalDate startDate, LocalDate endDate);

    /**
     * 获取点位违法趋势统计
     * 返回指定地理单位每天的统计数据
     * 例如：传入市级，返回该市每天的统计数据
     *
     * @param city 市
     * @param county 区县
     * @param township 镇街道
     * @param hamlet 社区/村
     * @param site 点位
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按天统计的趋势数据
     */
    SaResult getLocationTrends(String city, String county, String township,
        String hamlet, String site, LocalDate startDate, LocalDate endDate);

    /**
     * 获取点位违法趋势统计(按年统计)
     * 返回指定地理单位每月的统计数据
     * 例如：传入市级，返回该市每月的统计数据
     *
     * @param year 年份
     * @param city 市
     * @param county 区县
     * @param township 镇街道
     * @param hamlet 社区/村
     * @param site 点位
     * @return 按月统计的趋势数据(12个月)
     */
    SaResult getLocationTrendsByYear(int year, String city, String county, String township,
        String hamlet, String site);

    SaResult addVideo(MultipartFile[] file, Date parse, Integer vehicleId, String cameraName, String equipmentNumber);

    SaResult getAttendance(String city, String county, String township, String hamlet, String site, LocalDate date);

    SaResult addPlateNumber(String plateNumber, DateTime parse, Integer vehicleId, String equipmentNumber, String cameraName, Integer plateColor, MultipartFile[] files, MultipartFile[] framePath, Boolean isComing1, Boolean isComing2) throws IOException;

    SaResult getMapPointDetail(String city, String county, String township, String hamlet, String site);



    SaResult getHourlyViolationStats(String city, String county, String township, 
        String hamlet, String site,  Date startDate, Date endDate);

    /**
     * 获取月度违法类型分析数据
     */
    SaResult getViolationTypeAnalysisByMonth(String city, String county, String township, String hamlet, String site, Date date);

    SaResult getViolationTypeAnalysisByWeek(String city, String county, String township, String hamlet, String site);

    SaResult getViolationTypeAnalysisByYear(String city, String county, String township, String hamlet, String site, String year);

    SaResult getViolationTypeStatisticsByMap(String city, String county, String township, String hamlet, String site, Date startDate, Date endDate);

    SaResult getAttendanceAndWorkTimeByday(String city, String county, String township, String hamlet, String site, LocalDate date);

    SaResult getAttendanceAndWorkTimeByMonth(String city, String county, String township, String hamlet, String site, Date date);

    SaResult getAttendanceAndWorkTimeByYears(String city, String county, String township, String hamlet, String site, Date date);
}
