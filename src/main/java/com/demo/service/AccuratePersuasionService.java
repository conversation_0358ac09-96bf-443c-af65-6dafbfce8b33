package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.AccuratePersuasion;
import com.demo.entity.DTO.AccuratePersuasionDTO;
import com.demo.entity.DTO.PageBean;
import com.demo.entity.DTO.SubmitResults;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

public interface AccuratePersuasionService extends IService<AccuratePersuasion>{


    SaResult selectAccuratePersuasionList(AccuratePersuasionDTO accuratePersuasionDTO);

    SaResult selectAccuratePersuasionListMobile(Integer id, PageBean pageBean, Integer disposalStatus);

    SaResult submitAccuratePersuasion(MultipartFile[] files, SubmitResults submitResults);

    SaResult selectDispatchNumber(Integer userId);

    SaResult processingRate();

    SaResult getDisposalStatistics(String city, String county, String township, String hamlet, String site, String year);

    SaResult getDisposalStatisticsByMonth(String city, String county, String township, String hamlet, String site, Date startTime, Date endTime);

    SaResult dispatchAccuratePersuasion(MultipartFile[] files, Integer[] userIds, String[] userNames, String city, String county, String township, String hamlet, String site, Date termTime, String illegalName);

    SaResult uploadHidden(MultipartFile[] files, Integer userId, String userName, String city, String county, String township, String hamlet, String site, String illegalName);

    SaResult getDisposalEfficiencyAnalysisByDay(String city, String county, String township, String hamlet, String site, int year, int month, int day);

    SaResult getDisposalEfficiencyAnalysisByMonth(String city, String county, String township, String hamlet, String site, int year, int month);

    SaResult getDisposalEfficiencyAnalysisByYear(String city, String county, String township, String hamlet, String site, int year);
}
