package com.demo.service;

import cn.dev33.satoken.util.SaResult;

import java.util.Date;

/**
 * 请假服务接口
 */
public interface LeaveService {

    /**
     * 提交请假申请
     *
     * @param userId    员工ID
     * @param userName
     * @param leaveType 请假类型
     * @param startTime 请假开始时间
     * @param endTime   请假结束时间
     * @param reason    请假原因
     * @return 请假申请结果
     */
    SaResult applyLeave(Integer userId, String userName, Integer leaveType, Date startTime,
                        Date endTime, String reason);

    /**
     * 审批请假申请
     *
     * @param leaveId         请假记录ID
     * @param approvalStatus  审批状态
     * @param approvalComment 审批意见
     * @return 审批结果
     */
    SaResult approveLeave(Integer leaveId, Integer approvalStatus, String approvalComment);

    /**
     * 获取请假详情
     *
     * @param leaveId 请假记录ID
     * @return 请假详情
     */
    SaResult getLeaveDetail(Integer leaveId);

    /**
     * 取消请假申请
     *
     * @param leaveId 请假记录ID
     * @param reason  取消原因
     * @return 取消结果
     */
    SaResult cancelLeave(Integer leaveId, String reason);
    /**
     * 查询请假记录
     */
    SaResult getLeaveRecords(String city, String county, String township, String hamlet,
                             String site, String userName, Integer status, Date startDate, Date endDate, int page, int size);
}