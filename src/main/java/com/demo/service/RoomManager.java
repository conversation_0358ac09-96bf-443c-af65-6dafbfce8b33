package com.demo.service;

import org.springframework.stereotype.Component;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RoomManager {
    // 房间映射表
    private final Map<String, Room> rooms = new ConcurrentHashMap<>();
    
    public boolean createRoom(String roomId) {
        if (rooms.containsKey(roomId)) {
            return false;
        }
        rooms.put(roomId, new Room(roomId));
        return true;
    }
    
    public Room getRoom(String roomId) {
        return rooms.get(roomId);
    }
    
    public void addClientToRoom(String roomId, String clientId) {
        log.info("添加客户端到房间: roomId={}, clientId={}", roomId, clientId);
        rooms.computeIfAbsent(roomId, Room::new).addClient(clientId);
    }
    
    public void removeClientFromRoom(String roomId, String clientId) {
        Room room = rooms.get(roomId);
        if (room != null) {
            room.removeClient(clientId);
            if (room.getClients().isEmpty()) {
                rooms.remove(roomId);
            }
        }
    }
    
    public Set<String> getRoomClients(String roomId) {
        Room room = rooms.get(roomId);
        return room != null ? room.getClients() : Collections.emptySet();
    }
    
    public boolean roomExists(String roomId) {
        return rooms.containsKey(roomId);
    }
} 