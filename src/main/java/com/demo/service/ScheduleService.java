package com.demo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.Schedule;
import com.demo.entity.VO.ScheduleVO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface ScheduleService extends IService<Schedule> {
    // 获取员工排班
    List<ScheduleVO> getEmployeeSchedule(Integer userId, LocalDateTime startDate, LocalDateTime endDate);
    
    // 获取某天的排班
    List<Schedule> getScheduleByDate(LocalDateTime date);
    
    // 获取当天排班
    Schedule getTodaySchedule(Integer userId, LocalDateTime now);
    
    // 将Schedule转换为ScheduleVO
    ScheduleVO convertToVO(Schedule schedule);
    
    /**
     * 自动生成排班（基于用户当前班次组）
     */
    List<Schedule> generateSchedule(Integer userId, LocalDateTime startDate, Integer days);
    
    /**
     * 自动生成排班（指定班次组）
     */
    List<Schedule> generateSchedule(Integer userId, Integer groupId, LocalDateTime startDate, Integer days);

    /**
     * 检查排班是否已存在
     * @param userId 用户ID
     * @param scheduleDate 排班日期
     * @param shiftId 班次ID
     * @return 是否存在
     */
    boolean isScheduleExists(Integer userId, LocalDateTime scheduleDate, Integer shiftId);

    /**
     * 过滤已存在的排班记录
     * @param schedules 待检查的排班列表
     * @return 过滤后的排班列表（不包含重复的）
     */
    List<Schedule> filterExistingSchedules(List<Schedule> schedules);

    // 检查是否有排班依赖于某个班次
    boolean hasScheduleForShift(Integer shiftId);

    // 获取依赖于某个班次的用户ID
    List<Integer> getUserIdsByShiftId(Integer shiftId);

    // 获取用户当天的所有排班
    List<Schedule> getTodaySchedules(Integer userId, LocalDate date);
    // 获取员工排班(通过姓名)
    List<ScheduleVO> getEmployeeScheduleByUserName(String userName, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取指定地点应到人数
     */
    int getScheduledStaffCount(String city, String county, String township, String hamlet, String site, LocalDate date);

    /**
     * 获取指定地点实到人数
     */
    int getPresentStaffCount(String city, String county, String township, String hamlet, String site, LocalDate date);

    /**
     * 获取指定地点在岗人数
     */
    int getOnDutyStaffCount(String city, String county, String township, String hamlet, String site, LocalDate date);

    /**
     * 获取指定日期的排班记录
     */
    List<Schedule> getScheduleByDate(LocalDate date, int pageNum, int pageSize);

    /**
     * 获取用户当前的排班记录
     * @param userId 用户ID
     * @return 当前排班记录，如果没有则返回null
     */
    Schedule getCurrentSchedule(Integer userId);
}
