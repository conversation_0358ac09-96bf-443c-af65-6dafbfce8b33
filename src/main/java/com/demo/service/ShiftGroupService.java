package com.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.DTO.ShiftGroupDTO;
import com.demo.entity.ShiftGroup;

import java.util.List;

public interface ShiftGroupService extends IService<ShiftGroup> {
    
    /**
     * 获取所有启用状态的班次组
     */
    List<ShiftGroup> getActiveGroups();
    
    /**
     * 创建新的班次组
     */
    ShiftGroup createGroup(ShiftGroup group);
    
    /**
     * 更新班次组状态
     */
    boolean updateStatus(Integer id, Integer status);
    
    /**
     * 验证工作模式格式
     */
    boolean validateWorkPattern(String workPattern);
    /**
     * 获取班次组中的班次ID
     */
    List<Integer> getShiftIdsByGroupId(Integer id);


    IPage<ShiftGroupDTO> getActiveGroupsWithShifts(Page<ShiftGroup> pageRequest);
}