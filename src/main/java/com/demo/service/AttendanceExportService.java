package com.demo.service;

import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDate;

/**
 * 考勤数据导出服务
 */
public interface AttendanceExportService {
    
    /**
     * 导出人员考勤明细数据
     * 导出指定时间范围内、指定地区的人员考勤详细数据
     * 
     * @param city 市
     * @param county 县
     * @param township 乡镇
     * @param hamlet 村
     * @param site 点位
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param response HTTP响应对象
     */
    void exportPersonalAttendance(
        String city, String county, String township, String hamlet, String site,
        LocalDate startDate, LocalDate endDate,
        HttpServletResponse response
    ) throws IOException;
    
    /**
     * 导出考勤汇总数据（不包含人员明细）
     * 根据传入的地区层级进行汇总：
     * - 传入市级：汇总区级数据
     * - 传入市+区：汇总镇级数据
     * - 传入市+区+镇：汇总村级数据
     * - 传入市+区+镇+村：汇总人员数据
     * 
     * @param city 市
     * @param county 县
     * @param township 乡镇
     * @param hamlet 村
     * @param site 点位
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param response HTTP响应对象
     */
    void exportSummaryAttendance(
        String city, String county, String township, String hamlet, String site,
        LocalDate startDate, LocalDate endDate,
        HttpServletResponse response
    ) throws IOException;
    
    /**
     * 导出考勤明细和汇总数据（包含人员明细和汇总）
     * 根据传入的地区层级进行汇总和展示明细：
     * - 传入市级：汇总区级数据，显示各区县明细
     * - 传入市+区：汇总镇级数据，显示各镇明细
     * - 传入市+区+镇：汇总村级数据，显示各村明细
     * - 传入市+区+镇+村：汇总劝导站数据，显示各劝导站明细
     * - 传入市+区+镇+村+站点：显示该站点下所有人员明细
     * 
     * @param city 市
     * @param county 县
     * @param township 乡镇
     * @param hamlet 村
     * @param site 点位
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param response HTTP响应对象
     */
    void exportDetailAndSummary(
        String city, String county, String township, String hamlet, String site,
        LocalDate startDate, LocalDate endDate,
        HttpServletResponse response
    ) throws IOException;
} 