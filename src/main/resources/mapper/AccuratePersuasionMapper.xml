<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.AccuratePersuasionMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.AccuratePersuasion">
    <!--@mbg.generated-->
    <!--@Table `accurate_persuasion`-->
    <id column="UUID" jdbcType="VARCHAR" property="uuid" />
    <result column="illegal_records_uuid" jdbcType="VARCHAR" property="illegalRecordsUuid" />
    <result column="Illegal_name" jdbcType="VARCHAR" property="illegalName" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="disposal_method" jdbcType="VARCHAR" property="disposalMethod" />
    <result column="disposal_status" jdbcType="INTEGER" property="disposalStatus" />
    <result column="imgl" jdbcType="VARCHAR" property="imgl" />
    <result column="actual_processing" jdbcType="INTEGER" property="actualProcessing" />
    <result column="actual_processing_name" jdbcType="VARCHAR" property="actualProcessingName" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="processing_time" jdbcType="TIMESTAMP" property="processingTime" />
    <result column="deadline_time" jdbcType="TIMESTAMP" property="deadlineTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="township" jdbcType="VARCHAR" property="township" />
    <result column="hamlet" jdbcType="VARCHAR" property="hamlet" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="illegal_personnel_name" jdbcType="VARCHAR" property="illegalPersonnelName" />
    <result column="illegal_personnel_id_card" jdbcType="VARCHAR" property="illegalPersonnelIdCard" />
    <result column="illegal_personnel_phone" jdbcType="VARCHAR" property="illegalPersonnelPhone" />
    <result column="illegal_personnel_gender" jdbcType="VARCHAR" property="illegalPersonnelGender" />
      <result column="lower_faction_type" jdbcType="VARCHAR" property="lowerFactionType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `UUID`, `illegal_records_uuid`, `Illegal_name`, `user_id`, `user_name`, `disposal_method`, 
    `disposal_status`, `imgl`, `actual_processing`, `actual_processing_name`, `remarks`, 
    `processing_time`, `deadline_time`, `create_time`, `city`, `county`, `township`, 
    `hamlet`, `site`, `illegal_personnel_name`, `illegal_personnel_id_card`, `illegal_personnel_phone`, 
    `illegal_personnel_gender`
  </sql>


    <select id="selectAccuratePersuasionList" resultType="com.demo.entity.VO.AccuratePersuasionVO">
        select acc.UUID                                              as uuid,
               acc.illegal_name,
               acc.user_name,
               acc.disposal_method,
               acc.disposal_status,
               acc.imgl,
               acc.remarks,
               ir.illegal_type,
               DATE_FORMAT(acc.create_time, '%Y-%m-%d %H:%i:%s')     as create_time,
               DATE_FORMAT(acc.processing_time, '%Y-%m-%d %H:%i:%s') as processing_time,
               DATE_FORMAT(acc.deadline_time, '%Y-%m-%d %H:%i:%s')   as deadline_time
        from (    select UUID,
                         illegal_records_uuid,
                         Illegal_name,
                         user_id,
                         user_name,
                         disposal_method,
                         disposal_status,
                         imgl,
                         actual_processing,
                         actual_processing_name,
                         remarks,
                         processing_time,
                         deadline_time,
                         create_time
                  from accurate_persuasion
        <where>
            <if test="disposalMethod != null">
                and `disposal_method` = #{disposalMethod}
            </if>
            <if test="disposalStatus != null">
                and `disposal_status` = #{disposalStatus}
            </if>
            <if test="processingStartTime != null and processingEndTime != null">
                and `processing_time` between #{processingStartTime} and #{processingEndTime}
            </if>
            <if test="startTime != null and endTime != null">
                and `create_time` between #{startTime} and #{endTime}
            </if>
            <if test="userName != null">
                and `user_name` like concat('%', #{userName}, '%')
            </if>
        </where>
        ) acc
            left join illegal_records ir on acc.illegal_records_uuid = ir.uuid
        <where>
            <if test="illegalType != null">
                and ir.illegal_type = #{illegalType}
            </if>
        </where>
    </select>

    <select id="countTotal" resultType="int">
        SELECT COUNT(*) FROM accurate_persuasion
        WHERE create_time BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="countProcessed" resultType="int">
        SELECT COUNT(*) FROM accurate_persuasion
        WHERE create_time BETWEEN #{startDate} AND #{endDate}
        AND disposal_status = 1
    </select>

    <!-- 查询年内违法点位最多的前七个 -->
    <select id="selectTopLocationsByYear" resultType="map">
        SELECT location, COUNT(*) as count
        FROM accurate_persuasion
        WHERE YEAR(create_time) = #{year}
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 查询月内违法点位最多的前七个 -->
    <select id="selectTopLocationsByMonth" resultType="map">
        SELECT location, COUNT(*) as count
        FROM accurate_persuasion
        WHERE YEAR(create_time) = #{year} AND MONTH(create_time) = #{month}
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 查询日内违法点位最多的前七个 -->
    <select id="selectTopLocationsByDay" resultType="map">
        SELECT location, COUNT(*) as count
        FROM accurate_persuasion
        WHERE YEAR(create_time) = #{year} AND MONTH(create_time) = #{month} AND DAY(create_time) = #{day}
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 查询周内违法点位最多的前七个 -->
    <select id="selectTopLocationsByWeek" resultType="map">
        SELECT location, COUNT(*) as count
        FROM accurate_persuasion
        WHERE YEAR(create_time) = #{year} AND WEEK(create_time, 1) = #{week}
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 查询过去七天内违法点位最多的前七个 -->
    <select id="selectTopLocationsByLastSevenDays" resultType="map">
        SELECT location, COUNT(*) as count
        FROM accurate_persuasion
        WHERE create_time &gt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY location
        ORDER BY count DESC
        LIMIT 7
    </select>

    <!-- 获取按年统计的违法类型分布趋势 -->
    <select id="selectViolationTrendsByYear" resultType="map">
        SELECT illegal_type, MONTH(create_time) as period, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year}
        GROUP BY illegal_type, MONTH(create_time)
        ORDER BY period
    </select>

    <!-- 获取按月统计的违法类型分布趋势 -->
    <select id="selectViolationTrendsByMonth" resultType="map">
        SELECT illegal_type, DAY(create_time) as period, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year} AND MONTH(create_time) = #{month}
        GROUP BY illegal_type, DAY(create_time)
        ORDER BY period
    </select>

    <!-- 获取按周统计的违法类型分布趋势 -->
    <select id="selectViolationTrendsByWeek" resultType="map">
        SELECT illegal_type, DAYOFWEEK(create_time) as period, COUNT(*) as count
        FROM illegal_records
        WHERE YEAR(create_time) = #{year} AND WEEK(create_time, 1) = #{week}
        GROUP BY illegal_type, DAYOFWEEK(create_time)
        ORDER BY period
    </select>

    <select id="getProcessedCount" resultType="java.util.Map">
        WITH RECURSIVE
            date_range AS (
                -- 生成一个从今天开始到7天前的日期序列
                SELECT DATE_SUB(CURDATE(), INTERVAL 6 DAY) as date_value
                UNION ALL
                SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
                FROM date_range
                WHERE date_value &lt; CURDATE()),
        unprocessed_counts AS (
            -- 统计每个日期未处理的数量，使用create_time
        SELECT DATE(create_time) AS create_date,
        COUNT(DISTINCT illegal_records_uuid)         AS unprocessed_count
        FROM accurate_persuasion
        WHERE disposal_status = 1
          AND create_time &gt;= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
          AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY DATE(create_time)
        )
        -- 将日期序列与统计结果左连接
        SELECT DATE_FORMAT(d.date_value, '%m-%d') AS date,
               COALESCE(u.unprocessed_count, 0)   AS count
        FROM date_range d
                 LEFT JOIN unprocessed_counts u ON d.date_value = u.create_date
        ORDER BY d.date_value;
    </select>

    <select id="getUntreatedCount" resultType="java.util.Map">
        WITH RECURSIVE date_range AS (
            -- 生成一个从今天开始到7天前的日期序列
            SELECT DATE_SUB(CURDATE(), INTERVAL 6 DAY) as date_value
            UNION ALL
            SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
            FROM date_range
            WHERE date_value &lt; CURDATE()
        ),
        unprocessed_counts AS (
        SELECT
        DATE(create_time) AS create_date,
        COUNT(DISTINCT illegal_records_uuid) AS unprocessed_count
        FROM
        accurate_persuasion
        WHERE
        disposal_status = 0 -- 处理状态为未处理
        AND create_time &gt;= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
        AND create_time &lt; CURDATE() + INTERVAL 1 DAY
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY
        DATE(create_time)
        )
        -- 将日期序列与统计结果左连接
        SELECT
        DATE_FORMAT(d.date_value, '%m-%d') AS date, -- 格式化为日-月
        COALESCE(u.unprocessed_count, 0) AS count
        FROM
        date_range d
        LEFT JOIN unprocessed_counts u ON d.date_value = u.create_date
        ORDER BY
        d.date_value;
    </select>

    <select id="getProcessedCountByMonth" resultType="java.util.Map">
        WITH RECURSIVE DateSeries AS (
            SELECT CURDATE() - INTERVAL 29 DAY AS date
            UNION ALL
            SELECT date + INTERVAL 1 DAY
            FROM DateSeries
            WHERE date &lt; CURDATE()
        ),
        ProcessedTasks AS (
        SELECT
        DATE(ap.processing_time) AS date,
        ap.illegal_records_uuid
        FROM
        accurate_persuasion ap
        WHERE
        ap.processing_time >= CURDATE() - INTERVAL 29 DAY
        AND ap.disposal_status IN (1, 2, 3)
        AND ap.illegal_records_uuid IS NOT NULL
        )
        SELECT
        ds.date,
        COUNT(DISTINCT pt.illegal_records_uuid) AS number_of_processed_tasks
        FROM
        DateSeries ds
        LEFT JOIN
        ProcessedTasks pt ON ds.date = pt.date
        GROUP BY
        ds.date
        ORDER BY
        ds.date;
    </select>

    <select id="getUntreatedCountByMonth" resultType="java.util.Map">
        WITH RECURSIVE date_range AS (
            -- 构造从30天前到今天的日期序列
            SELECT CURDATE() - INTERVAL 30 DAY AS date_value
            UNION ALL
            SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
            FROM date_range
            WHERE date_value  &lt; CURDATE()
        ),
        unprocessed_counts AS (
        -- 统计每天去重后的未处理违法记录数量
        SELECT
        DATE(create_time) AS create_date,
        COUNT(DISTINCT illegal_records_uuid) AS unprocessed_count
        FROM accurate_persuasion
        WHERE
        disposal_status = 0 -- 仅统计未处理状态
        AND create_time >= CURDATE() - INTERVAL 30 DAY
        AND create_time  &lt;= CURDATE()
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY DATE(create_time)
        )
        -- 主查询：连接日期序列和统计数据
        SELECT
        DATE_FORMAT(d.date_value, '%m-%d') AS date,
        COALESCE(u.unprocessed_count, 0) AS count
        FROM date_range d
        LEFT JOIN unprocessed_counts u ON d.date_value = u.create_date
        ORDER BY d.date_value;
    </select>

  <select id="theProcessingRatesOfTheWeekBeforeLast" resultType="java.util.Map">
      SELECT COALESCE(total_count, 0)                AS total_count,
      COALESCE(completed_count, 0)            AS completed_count,
      COALESCE(completion_rate_percent, 0.00) AS completion_rate_percent
      FROM (
      SELECT COUNT(*)                                             AS total_count,
      SUM(CASE WHEN disposal_status = 1 THEN 1 ELSE 0 END) AS completed_count,
      ROUND(
      (SUM(CASE WHEN disposal_status = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)),
      2
      )                                                    AS completion_rate_percent
      FROM accurate_persuasion
      WHERE create_time >= DATE_SUB(NOW(), INTERVAL 14 DAY)
      AND create_time &lt; DATE_SUB(NOW(), INTERVAL 7 DAY)
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      ) AS subquery;
    </select>

  <select id="theProcessingRateOfThePreviousMonth" resultType="java.util.Map">
      SELECT COALESCE(total_count, 0)                AS total_count,
             COALESCE(completed_count, 0)            AS completed_count,
             COALESCE(completion_rate_percent, 0.00) AS completion_rate_percent
      FROM (
      SELECT COUNT(*)                                             AS total_count,
             SUM(CASE WHEN disposal_status = 1 THEN 1 ELSE 0 END) AS completed_count,
             ROUND(
                     (SUM(CASE WHEN disposal_status = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)),
                     2
             )                                                    AS completion_rate_percent
      FROM accurate_persuasion
      WHERE create_time >= DATE_SUB(NOW(), INTERVAL 60 DAY)
        AND create_time &lt; DATE_SUB(NOW(), INTERVAL 30 DAY)
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      ) AS subquery;
  </select>

  <select id="handleTheChartsOnScheduleOfTheWeek" resultType="java.util.Map">
      WITH RECURSIVE date_range AS (
          -- 构造从今天往前推7天的日期序列（包含今天）
          SELECT CURDATE() AS date_value
          UNION ALL
          SELECT DATE_SUB(date_value, INTERVAL 1 DAY)
          FROM date_range
          WHERE date_value > CURDATE() - INTERVAL 6 DAY
      ),
                     daily_stats AS (
                         -- 统计每天的总数和已完成数
                         SELECT
                             DATE(create_time) AS create_date,
                             COUNT(*) AS total_count,
                             SUM(CASE WHEN disposal_status = 1 THEN 1 ELSE 0 END) AS completed_count
                         FROM
                             accurate_persuasion
                         WHERE
                             create_time >= CURDATE() - INTERVAL 7 DAY
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
                         GROUP BY
                             DATE(create_time)
                     )
-- 主查询：计算完成率，并对空值补 0
      SELECT
          DATE_FORMAT(d.date_value, '%m-%d') AS date,
          ROUND(
                  CASE
                      WHEN COALESCE(s.total_count, 0) = 0 THEN 0.00
                      ELSE (s.completed_count * 100.0 / s.total_count)
                      END,
                  2
          ) AS completion_rate_percent
      FROM
          date_range d
              LEFT JOIN daily_stats s ON d.date_value = s.create_date
      ORDER BY
          d.date_value;
  </select>

  <select id="handleTheChartsOnScheduleOfThePreviousMonth" resultType="java.util.Map">
      WITH RECURSIVE date_range AS (
          -- 构造从30天前到今天的日期序列
          SELECT CURDATE() - INTERVAL 30 DAY AS date_value
          UNION ALL
          SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
          FROM date_range
          WHERE date_value &lt; CURDATE()
      ),
      daily_stats AS (
      -- 统计每天的总数和已完成数
      SELECT
      DATE(create_time) AS create_date,
      COUNT(*) AS total_count,
      SUM(CASE WHEN disposal_status = 1 THEN 1 ELSE 0 END) AS completed_count
      FROM
      accurate_persuasion
      WHERE
      create_time >= CURDATE() - INTERVAL 30 DAY
      AND create_time &lt;= CURDATE()
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      GROUP BY DATE(create_time)
      )
      -- 主查询：仅返回 date 和 completion_rate_percent
      SELECT
      DATE_FORMAT(d.date_value, '%m-%d') AS date,
      ROUND(
      CASE
      WHEN COALESCE(s.total_count, 0) = 0 THEN 0.00
      ELSE (s.completed_count * 100.0 / s.total_count)
      END,
      2
      ) AS completion_rate_percent
      FROM date_range d
      LEFT JOIN daily_stats s ON d.date_value = s.create_date
      ORDER BY d.date_value;
    </select>

  <select id="dealWithItOnScheduleByMonth" resultType="java.util.Map">
      WITH RECURSIVE date_range AS (
          -- 生成从 60 天前到 30 天前的日期序列
          SELECT CURDATE() - INTERVAL 60 DAY AS date_value
          UNION ALL
          SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
          FROM date_range
          WHERE date_value 	&lt; CURDATE() - INTERVAL 31 DAY
      ),
      daily_stats AS (
      -- 统计每天的总数和已完成数
      SELECT
      DATE(create_time) AS create_date,
      COUNT(*) AS total_count,
      SUM(CASE WHEN disposal_status = 1 THEN 1 ELSE 0 END) AS completed_count
      FROM
      accurate_persuasion
      WHERE
      create_time >= CURDATE() - INTERVAL 60 DAY
      AND create_time 	&lt; CURDATE() - INTERVAL 30 DAY
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      GROUP BY DATE(create_time)
      )
      -- 主查询：仅返回 date 和 completion_rate_percent
      SELECT
      DATE_FORMAT(d.date_value, '%m-%d') AS date,
      ROUND(
      CASE
      WHEN COALESCE(s.total_count, 0) = 0 THEN 0.00
      ELSE (s.completed_count * 100.0 / s.total_count)
      END,
      2
      ) AS completion_rate_percent
      FROM date_range d
      LEFT JOIN daily_stats s ON d.date_value = s.create_date
      ORDER BY d.date_value;
    </select>

  <select id="dealWithItOnScheduleByWeek" resultType="java.util.Map">
      WITH RECURSIVE date_range AS (
          -- 构造从 14 天前到 7 天前的日期序列
          SELECT CURDATE() - INTERVAL 14 DAY AS date_value
          UNION ALL
          SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
          FROM date_range
          WHERE date_value &lt; CURDATE() - INTERVAL 8 DAY -- 确保不超出到第7天当天
      ),
      daily_stats AS (
      -- 统计每天的总数和已完成数
      SELECT
      DATE(create_time) AS create_date,
      COUNT(*) AS total_count,
      SUM(CASE WHEN disposal_status = 1 THEN 1 ELSE 0 END) AS completed_count
      FROM
      accurate_persuasion
      WHERE
      create_time >= DATE_SUB(CURDATE(), INTERVAL 14 DAY)
      AND create_time &lt; DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      GROUP BY DATE(create_time)
      )
      -- 主查询：仅返回 date 和 completion_rate_percent
      SELECT
      DATE_FORMAT(d.date_value, '%m-%d') AS date,
      ROUND(
      CASE
      WHEN COALESCE(s.total_count, 0) = 0 THEN 0.00
      ELSE (s.completed_count * 100.0 / s.total_count)
      END,
      2
      ) AS completion_rate_percent
      FROM date_range d
      LEFT JOIN daily_stats s ON d.date_value = s.create_date
      ORDER BY d.date_value;
    </select>

  <select id="numberOfTasksByMonth" resultType="java.util.Map">
      WITH RECURSIVE DateSeries AS (
          SELECT CURDATE() - INTERVAL 29 DAY AS date
          UNION ALL
          SELECT date + INTERVAL 1 DAY
          FROM DateSeries
          WHERE date &lt; CURDATE()
      ),
      DailyTasks AS (
      SELECT
      DATE(ap.create_time) AS date,
      ap.illegal_records_uuid
      FROM
      accurate_persuasion ap
      WHERE
      ap.create_time >= CURDATE() - INTERVAL 29 DAY
      AND ap.illegal_records_uuid IS NOT NULL
      )
      SELECT
      ds.date,
      COUNT(DISTINCT dt.illegal_records_uuid) AS number_of_tasks
      FROM
      DateSeries ds
      LEFT JOIN
      DailyTasks dt ON ds.date = dt.date
      GROUP BY
      ds.date
      ORDER BY
      ds.date;
    </select>

  <select id="taskNumberProcessingNumberProcessingRateByMonth" resultType="java.util.Map">
      WITH RECURSIVE date_range AS (
      SELECT DATE_FORMAT(#{startTime}, '%Y-%m-%d') AS `date`
      UNION ALL
      SELECT DATE_FORMAT(DATE_ADD(`date`, INTERVAL 1 DAY), '%Y-%m-%d')
      FROM date_range
      WHERE DATE_ADD(`date`, INTERVAL 1 DAY) &lt;= #{endTime}
      ),
          task_stats_by_create_date AS (
          SELECT
          DATE(create_time) AS create_date,
          COUNT(DISTINCT illegal_records_uuid) AS total_tasks,
          COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS completed_tasks,
          COUNT(DISTINCT CASE WHEN disposal_status = 1 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS on_time_tasks,
          COUNT(DISTINCT CASE WHEN disposal_status = 2 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS overdue_tasks,
          COUNT(DISTINCT CASE WHEN disposal_status = 3 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS canceled_tasks
          FROM accurate_persuasion
          WHERE
          DATE(create_time) BETWEEN #{startTime} AND #{endTime}
          AND illegal_records_uuid IS NOT NULL
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
          GROUP BY DATE(create_time)
          ),
          task_stats_by_complete_date AS (
          SELECT
          DATE(processing_time) AS complete_date,
          COUNT(DISTINCT illegal_records_uuid) AS completed_today_total
          FROM accurate_persuasion
          WHERE
          DATE(processing_time) BETWEEN #{startTime} AND #{endTime}
          AND disposal_status IN (1, 2)
          AND illegal_records_uuid IS NOT NULL
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
          GROUP BY DATE(processing_time)
          )
          SELECT
          d.`date` AS `date`,
          COALESCE(t1.total_tasks, 0) AS `totalTasks`,
          COALESCE(t1.completed_tasks, 0) AS `completedTasks`,
          COALESCE(t1.on_time_tasks, 0) AS `onTimeTasks`,
          COALESCE(t1.overdue_tasks, 0) AS `overdueTasks`,
          COALESCE(t1.canceled_tasks, 0) AS `canceledTasks`,
          COALESCE(t2.completed_today_total, 0) AS `completedTodayTotal`
          FROM date_range d
          LEFT JOIN task_stats_by_create_date t1 ON d.`date` = t1.create_date
          LEFT JOIN task_stats_by_complete_date t2 ON d.`date` = t2.complete_date
          ORDER BY d.`date`;
    </select>

  <select id="onTimeCompletionRateByMonth" resultType="java.util.Map">
      WITH RECURSIVE
          date_range AS (
              -- 构造前60~30天之间的日期序列（共30天）
              SELECT CURDATE() - INTERVAL 60 DAY AS date_value
              UNION ALL
              SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
              FROM date_range
              WHERE date_value &lt; CURDATE() - INTERVAL 31 DAY),
      base_tasks AS (
          -- 获取前60~30天之间每天下派的任务，并标记是否完成
      SELECT DATE(create_time)                                          AS create_date,
             illegal_records_uuid,
             MAX(CASE WHEN disposal_status IN (1, 2) THEN 1 ELSE 0 END) AS is_completed
      FROM accurate_persuasion
      WHERE DATE(create_time) >= CURDATE() - INTERVAL 60 DAY
        AND DATE(create_time) &lt; CURDATE() - INTERVAL 30 DAY
        AND illegal_records_uuid IS NOT NULL
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      GROUP BY DATE(create_time), illegal_records_uuid
      ),
          daily_stats AS (
              -- 按创建日期分组，统计总数和完成数
              SELECT create_date,
                     COUNT(illegal_records_uuid) AS created_count,
                     SUM(is_completed)           AS completed_count
              FROM base_tasks
              GROUP BY create_date)
      -- 主查询：左连接日期范围，仅返回 date 和 完成率
      SELECT DATE_FORMAT(dr.date_value, '%m-%d') AS date,
             ROUND(
                     COALESCE(ds.completed_count * 100.0 / NULLIF(ds.created_count, 0), 0),
                     2
             )             AS onTimeCompletionRate
      FROM date_range dr
               LEFT JOIN daily_stats ds ON dr.date_value = ds.create_date
      ORDER BY dr.date_value;
  </select>

  <select id="taskNumberProcessingNumberProcessingRateByYear" resultType="java.util.Map">
      WITH RECURSIVE month_range AS (
      SELECT CAST(CONCAT(#{year}, '-01-01') AS DATE) AS month_date
      UNION ALL
      SELECT DATE_ADD(month_date, INTERVAL 1 MONTH)
      FROM month_range
      WHERE month_date &lt; CAST(CONCAT(#{year}, '-12-01') AS DATE)
      ),
      task_stats_by_create_month AS (
      SELECT
      DATE_FORMAT(create_time, '%Y-%m') AS create_month,
      COUNT(DISTINCT illegal_records_uuid) AS total_tasks,
      COUNT(DISTINCT CASE
      WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL
      THEN illegal_records_uuid
      END) AS completed_tasks,
      COUNT(DISTINCT CASE
      WHEN disposal_status = 1 AND processing_time IS NOT NULL
      THEN illegal_records_uuid
      END) AS on_time_tasks,
      COUNT(DISTINCT CASE
      WHEN disposal_status = 2 AND processing_time IS NOT NULL
      THEN illegal_records_uuid
      END) AS overdue_tasks,
      COUNT(DISTINCT CASE
      WHEN disposal_status = 3 AND processing_time IS NOT NULL
      THEN illegal_records_uuid
      END) AS canceled_tasks
      FROM accurate_persuasion
      WHERE create_time >= CAST(CONCAT(#{year}, '-01-01') AS DATE)
      AND create_time &lt; CAST(CONCAT(#{year}+1, '-01-01') AS DATE)
      AND illegal_records_uuid IS NOT NULL
      <!-- 地址筛选 -->
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      GROUP BY DATE_FORMAT(create_time, '%Y-%m')
      ),
      task_stats_by_complete_month AS (
      SELECT
      DATE_FORMAT(processing_time, '%Y-%m') AS complete_month,
      COUNT(DISTINCT illegal_records_uuid) AS completed_today_total
      FROM accurate_persuasion
      WHERE processing_time >= CAST(CONCAT(#{year}, '-01-01') AS DATE)
      AND processing_time &lt; CAST(CONCAT(#{year}+1, '-01-01') AS DATE)
      AND disposal_status IN (1, 2)
      AND illegal_records_uuid IS NOT NULL
      <!-- 地址筛选 -->
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      GROUP BY DATE_FORMAT(processing_time, '%Y-%m')
      )
      SELECT
      DATE_FORMAT(m.month_date, '%Y-%m') AS month,
      COALESCE(t1.total_tasks, 0) AS totalTasks,
      COALESCE(t1.completed_tasks, 0) AS completedTasks,
      COALESCE(t1.on_time_tasks, 0) AS onTimeTasks,
      COALESCE(t1.overdue_tasks, 0) AS overdueTasks,
      COALESCE(t1.canceled_tasks, 0) AS canceledTasks,
      COALESCE(t2.completed_today_total, 0) AS completedTodayTotal
      FROM month_range m
      LEFT JOIN task_stats_by_create_month t1 ON DATE_FORMAT(m.month_date, '%Y-%m') = t1.create_month
      LEFT JOIN task_stats_by_complete_month t2 ON DATE_FORMAT(m.month_date, '%Y-%m') = t2.complete_month
      ORDER BY m.month_date
    </select>

  <select id="onTimeCompletionRateByWeek" resultType="java.util.Map">
      WITH RECURSIVE
          date_range AS (
              -- 构造从14天前到7天前的日期序列（共7天）
              SELECT CURDATE() - INTERVAL 14 DAY AS date_value
              UNION ALL
              SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
              FROM date_range
              WHERE date_value &lt; CURDATE() - INTERVAL 7 DAY
      ),
      base_tasks AS (
      -- 获取14~7天之间每天下派的任务，并标记是否完成
      SELECT
      DATE(create_time) AS create_date,
      illegal_records_uuid,
      MAX(CASE WHEN disposal_status IN (1, 2) THEN 1 ELSE 0 END) AS is_completed
      FROM accurate_persuasion
      WHERE DATE(create_time) >= CURDATE() - INTERVAL 14 DAY
      AND DATE(create_time) &lt; CURDATE() - INTERVAL 6 DAY
      AND illegal_records_uuid IS NOT NULL
      <if test="city != null and city != ''">
          AND city = #{city}
      </if>
      <if test="county != null and county != ''">
          AND county = #{county}
      </if>
      <if test="township != null and township != ''">
          AND township = #{township}
      </if>
      <if test="hamlet != null and hamlet != ''">
          AND hamlet = #{hamlet}
      </if>
      <if test="site != null and site != ''">
          AND site = #{site}
      </if>
      GROUP BY DATE(create_time), illegal_records_uuid
      ),
      daily_stats AS (
      -- 按创建日期分组，统计总数和完成数
      SELECT
      create_date,
      COUNT(illegal_records_uuid) AS created_count,
      SUM(is_completed) AS completed_count
      FROM base_tasks
      GROUP BY create_date
      )
      -- 主查询：左连接日期范围，仅返回 date 和 完成率
      SELECT
      DATE_FORMAT(dr.date_value, '%m-%d') AS date,
      ROUND(
      COALESCE(ds.completed_count * 100.0 / NULLIF(ds.created_count, 0), 0),
      2
      ) AS onTimeCompletionRate
      FROM date_range dr
      LEFT JOIN daily_stats ds ON dr.date_value = ds.create_date
      ORDER BY dr.date_value;
    </select>

    <select id="getDisposalEfficiencyAnalysisByDay" resultType="java.util.Map">
        SELECT
        <choose>
            <!-- 如果传入city，按county分组 -->
            <when test="city != null and city != '' and (county == null or county == '')">
                county AS region,
            </when>
            <!-- 如果传入county，按township分组 -->
            <when test="county != null and county != '' and (township == null or township == '')">
                township AS region,
            </when>
            <!-- 如果传入township，按hamlet分组 -->
            <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                hamlet AS region,
            </when>
            <!-- 如果传入hamlet，按site分组 -->
            <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                site AS region,
            </when>
            <!-- 默认情况，如果没有传入任何地域参数，按city分组 -->
            <otherwise>
                city AS region,
            </otherwise>
        </choose>
        COUNT(DISTINCT illegal_records_uuid)                                                                        AS totalTasks,
        COUNT(DISTINCT CASE
                           WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL
                               THEN illegal_records_uuid END)                                                       AS completedTasks,
        COUNT(DISTINCT CASE
                           WHEN disposal_status = 1 AND processing_time IS NOT NULL
                               THEN illegal_records_uuid END)                                                       AS onTimeTasks,
        COUNT(DISTINCT CASE
                           WHEN disposal_status = 2 AND processing_time IS NOT NULL
                               THEN illegal_records_uuid END)                                                       AS overdueTasks,
        COUNT(DISTINCT CASE
                           WHEN disposal_status = 3 AND processing_time IS NOT NULL
                               THEN illegal_records_uuid END)                                                       AS canceledTasks,
        ROUND(
                CASE
                    WHEN COUNT(DISTINCT illegal_records_uuid) = 0 THEN 0.00
                    ELSE (COUNT(DISTINCT CASE
                                             WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL
                                                 THEN illegal_records_uuid END) * 100.0 /
                          COUNT(DISTINCT illegal_records_uuid))
                    END,
                2
        )                                                                                                           AS completionRate,
        ROUND(
                CASE
                    WHEN COUNT(DISTINCT CASE
                                            WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL
                                                THEN illegal_records_uuid END) = 0 THEN 0.00
                    ELSE (COUNT(DISTINCT CASE
                                             WHEN disposal_status = 1 AND processing_time IS NOT NULL
                                                 THEN illegal_records_uuid END) * 100.0 / COUNT(DISTINCT CASE
                                                                                                             WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL
                                                                                                                 THEN illegal_records_uuid END))
                    END,
                2
        )                                                                                                           AS onTimeRate
        FROM accurate_persuasion
        WHERE DATE(create_time) = CONCAT(#{year}, '-', LPAD(#{month}, 2, '0'), '-', LPAD(#{day}, 2, '0'))
          AND illegal_records_uuid IS NOT NULL
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY
        <choose>
            <!-- 根据传入参数确定分组字段 -->
            <when test="city != null and city != '' and (county == null or county == '')">
                county
            </when>
            <when test="county != null and county != '' and (township == null or township == '')">
                township
            </when>
            <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                hamlet
            </when>
            <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                site
            </when>
            <otherwise>
                city
            </otherwise>
        </choose>
        ORDER BY totalTasks DESC
    </select>

    <select id="getDisposalEfficiencyAnalysisByMonth" resultType="java.util.Map">
        SELECT
        <choose>
            <!-- 如果传入city，按county分组 -->
            <when test="city != null and city != '' and (county == null or county == '')">
                county AS region,
            </when>
            <!-- 如果传入county，按township分组 -->
            <when test="county != null and county != '' and (township == null or township == '')">
                township AS region,
            </when>
            <!-- 如果传入township，按hamlet分组 -->
            <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                hamlet AS region,
            </when>
            <!-- 如果传入hamlet，按site分组 -->
            <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                site AS region,
            </when>
            <!-- 默认情况，如果没有传入任何地域参数，按city分组 -->
            <otherwise>
                city AS region,
            </otherwise>
        </choose>
        COUNT(DISTINCT illegal_records_uuid) AS totalTasks,
        COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS completedTasks,
        COUNT(DISTINCT CASE WHEN disposal_status = 1 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS onTimeTasks,
        COUNT(DISTINCT CASE WHEN disposal_status = 2 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS overdueTasks,
        COUNT(DISTINCT CASE WHEN disposal_status = 3 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS canceledTasks,
        ROUND(
            CASE
                WHEN COUNT(DISTINCT illegal_records_uuid) = 0 THEN 0.00
                ELSE (COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) * 100.0 / COUNT(DISTINCT illegal_records_uuid))
            END,
            2
        ) AS completionRate,
        ROUND(
            CASE
                WHEN COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) = 0 THEN 0.00
                ELSE (COUNT(DISTINCT CASE WHEN disposal_status = 1 AND processing_time IS NOT NULL THEN illegal_records_uuid END) * 100.0 / COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END))
            END,
            2
        ) AS onTimeRate
        FROM accurate_persuasion
        WHERE YEAR(create_time) = #{year} AND MONTH(create_time) = #{month}
        AND illegal_records_uuid IS NOT NULL
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY
        <choose>
            <!-- 根据传入参数确定分组字段 -->
            <when test="city != null and city != '' and (county == null or county == '')">
                county
            </when>
            <when test="county != null and county != '' and (township == null or township == '')">
                township
            </when>
            <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                hamlet
            </when>
            <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                site
            </when>
            <otherwise>
                city
            </otherwise>
        </choose>
        ORDER BY totalTasks DESC
    </select>

    <select id="getDisposalEfficiencyDailyDetailsByMonth" resultType="java.util.Map">
        WITH RECURSIVE date_range AS (
            -- 生成指定月份的所有日期
            SELECT CAST(CONCAT(#{year}, '-', LPAD(#{month}, 2, '0'), '-01') AS DATE) AS date_value
            UNION ALL
            SELECT DATE_ADD(date_value, INTERVAL 1 DAY)
            FROM date_range
            WHERE date_value &lt; LAST_DAY(CAST(CONCAT(#{year}, '-', LPAD(#{month}, 2, '0'), '-01') AS DATE))
        ),
        daily_stats AS (
            SELECT
            <choose>
                <!-- 如果传入city，按county分组 -->
                <when test="city != null and city != '' and (county == null or county == '')">
                    county AS region,
                </when>
                <!-- 如果传入county，按township分组 -->
                <when test="county != null and county != '' and (township == null or township == '')">
                    township AS region,
                </when>
                <!-- 如果传入township，按hamlet分组 -->
                <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                    hamlet AS region,
                </when>
                <!-- 如果传入hamlet，按site分组 -->
                <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                    site AS region,
                </when>
                <!-- 默认情况，如果没有传入任何地域参数，按city分组 -->
                <otherwise>
                    city AS region,
                </otherwise>
            </choose>
            DATE_FORMAT(create_time, '%Y-%m-%d') AS date,
            COUNT(DISTINCT illegal_records_uuid) AS dailyTotalTasks,
            COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS dailyCompletedTasks,
            COUNT(DISTINCT CASE WHEN disposal_status = 1 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS dailyOnTimeTasks,
            COUNT(DISTINCT CASE WHEN disposal_status = 2 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS dailyOverdueTasks,
            COUNT(DISTINCT CASE WHEN disposal_status = 3 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS dailyCanceledTasks
            FROM accurate_persuasion
            WHERE YEAR(create_time) = #{year} AND MONTH(create_time) = #{month}
            AND illegal_records_uuid IS NOT NULL
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
            GROUP BY
            <choose>
                <!-- 根据传入参数确定分组字段 -->
                <when test="city != null and city != '' and (county == null or county == '')">
                    county,
                </when>
                <when test="county != null and county != '' and (township == null or township == '')">
                    township,
                </when>
                <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                    hamlet,
                </when>
                <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                    site,
                </when>
                <otherwise>
                    city,
                </otherwise>
            </choose>
            DATE(create_time)
        ),
        all_regions AS (
            -- 获取所有地域
            SELECT DISTINCT
            <choose>
                <when test="city != null and city != '' and (county == null or county == '')">
                    county AS region
                </when>
                <when test="county != null and county != '' and (township == null or township == '')">
                    township AS region
                </when>
                <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                    hamlet AS region
                </when>
                <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                    site AS region
                </when>
                <otherwise>
                    city AS region
                </otherwise>
            </choose>
            FROM accurate_persuasion
            WHERE YEAR(create_time) = #{year} AND MONTH(create_time) = #{month}
            AND illegal_records_uuid IS NOT NULL
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
        )
        SELECT
            ar.region,
            DATE_FORMAT(dr.date_value, '%Y-%m-%d') AS date,
            COALESCE(ds.dailyTotalTasks, 0) AS dailyTotalTasks,
            COALESCE(ds.dailyCompletedTasks, 0) AS dailyCompletedTasks,
            COALESCE(ds.dailyOnTimeTasks, 0) AS dailyOnTimeTasks,
            COALESCE(ds.dailyOverdueTasks, 0) AS dailyOverdueTasks,
            COALESCE(ds.dailyCanceledTasks, 0) AS dailyCanceledTasks,
            ROUND(
                CASE
                    WHEN COALESCE(ds.dailyTotalTasks, 0) = 0 THEN 0.00
                    ELSE (COALESCE(ds.dailyCompletedTasks, 0) * 100.0 / COALESCE(ds.dailyTotalTasks, 0))
                END,
                2
            ) AS dailyCompletionRate,
            ROUND(
                CASE
                    WHEN COALESCE(ds.dailyCompletedTasks, 0) = 0 THEN 0.00
                    ELSE (COALESCE(ds.dailyOnTimeTasks, 0) * 100.0 / COALESCE(ds.dailyCompletedTasks, 0))
                END,
                2
            ) AS dailyOnTimeRate
        FROM all_regions ar
        CROSS JOIN date_range dr
        LEFT JOIN daily_stats ds ON ar.region = ds.region AND dr.date_value = ds.date
        ORDER BY ar.region, dr.date_value
    </select>

    <select id="getDisposalEfficiencyAnalysisByYear" resultType="java.util.Map">
        SELECT
        <choose>
            <!-- 如果传入city，按county分组 -->
            <when test="city != null and city != '' and (county == null or county == '')">
                county AS region,
            </when>
            <!-- 如果传入county，按township分组 -->
            <when test="county != null and county != '' and (township == null or township == '')">
                township AS region,
            </when>
            <!-- 如果传入township，按hamlet分组 -->
            <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                hamlet AS region,
            </when>
            <!-- 如果传入hamlet，按site分组 -->
            <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                site AS region,
            </when>
            <!-- 默认情况，如果没有传入任何地域参数，按city分组 -->
            <otherwise>
                city AS region,
            </otherwise>
        </choose>
        COUNT(DISTINCT illegal_records_uuid) AS totalTasks,
        COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS completedTasks,
        COUNT(DISTINCT CASE WHEN disposal_status = 1 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS onTimeTasks,
        COUNT(DISTINCT CASE WHEN disposal_status = 2 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS overdueTasks,
        COUNT(DISTINCT CASE WHEN disposal_status = 3 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS canceledTasks,
        ROUND(
            CASE
                WHEN COUNT(DISTINCT illegal_records_uuid) = 0 THEN 0.00
                ELSE (COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) * 100.0 / COUNT(DISTINCT illegal_records_uuid))
            END,
            2
        ) AS completionRate,
        ROUND(
            CASE
                WHEN COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) = 0 THEN 0.00
                ELSE (COUNT(DISTINCT CASE WHEN disposal_status = 1 AND processing_time IS NOT NULL THEN illegal_records_uuid END) * 100.0 / COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END))
            END,
            2
        ) AS onTimeRate
        FROM accurate_persuasion
        WHERE YEAR(create_time) = #{year}
        AND illegal_records_uuid IS NOT NULL
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        GROUP BY
        <choose>
            <!-- 根据传入参数确定分组字段 -->
            <when test="city != null and city != '' and (county == null or county == '')">
                county
            </when>
            <when test="county != null and county != '' and (township == null or township == '')">
                township
            </when>
            <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                hamlet
            </when>
            <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                site
            </when>
            <otherwise>
                city
            </otherwise>
        </choose>
        ORDER BY totalTasks DESC
    </select>

    <select id="getDisposalEfficiencyMonthlyDetailsByYear" resultType="java.util.Map">
        WITH RECURSIVE month_range AS (
            -- 生成指定年份的所有月份
            SELECT CAST(CONCAT(#{year}, '-01-01') AS DATE) AS month_date
            UNION ALL
            SELECT DATE_ADD(month_date, INTERVAL 1 MONTH)
            FROM month_range
            WHERE month_date &lt; CAST(CONCAT(#{year}, '-12-01') AS DATE)
        ),
        monthly_stats AS (
            SELECT
            <choose>
                <!-- 如果传入city，按county分组 -->
                <when test="city != null and city != '' and (county == null or county == '')">
                    county AS region,
                </when>
                <!-- 如果传入county，按township分组 -->
                <when test="county != null and county != '' and (township == null or township == '')">
                    township AS region,
                </when>
                <!-- 如果传入township，按hamlet分组 -->
                <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                    hamlet AS region,
                </when>
                <!-- 如果传入hamlet，按site分组 -->
                <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                    site AS region,
                </when>
                <!-- 默认情况，如果没有传入任何地域参数，按city分组 -->
                <otherwise>
                    city AS region,
                </otherwise>
            </choose>
            DATE_FORMAT(create_time, '%Y-%m') AS month,
            COUNT(DISTINCT illegal_records_uuid) AS monthlyTotalTasks,
            COUNT(DISTINCT CASE WHEN disposal_status IN (1, 2) AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS monthlyCompletedTasks,
            COUNT(DISTINCT CASE WHEN disposal_status = 1 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS monthlyOnTimeTasks,
            COUNT(DISTINCT CASE WHEN disposal_status = 2 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS monthlyOverdueTasks,
            COUNT(DISTINCT CASE WHEN disposal_status = 3 AND processing_time IS NOT NULL THEN illegal_records_uuid END) AS monthlyCanceledTasks
            FROM accurate_persuasion
            WHERE YEAR(create_time) = #{year}
            AND illegal_records_uuid IS NOT NULL
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
            GROUP BY
            <choose>
                <!-- 根据传入参数确定分组字段 -->
                <when test="city != null and city != '' and (county == null or county == '')">
                    county,
                </when>
                <when test="county != null and county != '' and (township == null or township == '')">
                    township,
                </when>
                <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                    hamlet,
                </when>
                <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                    site,
                </when>
                <otherwise>
                    city,
                </otherwise>
            </choose>
            DATE_FORMAT(create_time, '%Y-%m')
        ),
        all_regions AS (
            -- 获取所有地域
            SELECT DISTINCT
            <choose>
                <when test="city != null and city != '' and (county == null or county == '')">
                    county AS region
                </when>
                <when test="county != null and county != '' and (township == null or township == '')">
                    township AS region
                </when>
                <when test="township != null and township != '' and (hamlet == null or hamlet == '')">
                    hamlet AS region
                </when>
                <when test="hamlet != null and hamlet != '' and (site == null or site == '')">
                    site AS region
                </when>
                <otherwise>
                    city AS region
                </otherwise>
            </choose>
            FROM accurate_persuasion
            WHERE YEAR(create_time) = #{year}
            AND illegal_records_uuid IS NOT NULL
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND site = #{site}
            </if>
        )
        SELECT
            ar.region,
            DATE_FORMAT(mr.month_date, '%Y-%m') AS month,
            COALESCE(ms.monthlyTotalTasks, 0) AS monthlyTotalTasks,
            COALESCE(ms.monthlyCompletedTasks, 0) AS monthlyCompletedTasks,
            COALESCE(ms.monthlyOnTimeTasks, 0) AS monthlyOnTimeTasks,
            COALESCE(ms.monthlyOverdueTasks, 0) AS monthlyOverdueTasks,
            COALESCE(ms.monthlyCanceledTasks, 0) AS monthlyCanceledTasks,
            ROUND(
                CASE
                    WHEN COALESCE(ms.monthlyTotalTasks, 0) = 0 THEN 0.00
                    ELSE (COALESCE(ms.monthlyCompletedTasks, 0) * 100.0 / COALESCE(ms.monthlyTotalTasks, 0))
                END,
                2
            ) AS monthlyCompletionRate,
            ROUND(
                CASE
                    WHEN COALESCE(ms.monthlyCompletedTasks, 0) = 0 THEN 0.00
                    ELSE (COALESCE(ms.monthlyOnTimeTasks, 0) * 100.0 / COALESCE(ms.monthlyCompletedTasks, 0))
                END,
                2
            ) AS monthlyOnTimeRate
        FROM all_regions ar
        CROSS JOIN month_range mr
        LEFT JOIN monthly_stats ms ON ar.region = ms.region AND DATE_FORMAT(mr.month_date, '%Y-%m') = ms.month
        ORDER BY ar.region, mr.month_date
    </select>
</mapper>