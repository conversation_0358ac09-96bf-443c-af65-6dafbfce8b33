<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.TreeNodeMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.TreeNode">
    <!--@mbg.generated-->
    <!--@Table `tree_node`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parentId" jdbcType="INTEGER" property="parentId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `parentId`, `label`, `update_time`, `create_time`
  </sql>
</mapper>