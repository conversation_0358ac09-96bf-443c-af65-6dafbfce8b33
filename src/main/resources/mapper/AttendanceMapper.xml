<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.AttendanceMapper">

    <!-- 根据排班ID查询所有考勤记录 -->
    <select id="selectByScheduleId" resultType="com.demo.entity.Attendance">
        SELECT * FROM attendance
        WHERE schedule_id = #{scheduleId}
    </select>
    
    <!-- 批量查询用户的最后一次打卡记录 -->
    <select id="selectLastRecordBatch" resultType="com.demo.entity.Attendance">
        SELECT a.*
        FROM (
            SELECT 
                a.*,
                ROW_NUMBER() OVER(PARTITION BY a.user_id ORDER BY a.update_time DESC) as rn
            FROM attendance a
            WHERE a.user_id IN 
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            AND DATE(create_time) = CURDATE()  <!-- 确保是当天创建的记录 -->
            AND type IN (0, 5)  <!-- 包含正常签到(0)和重复签到(5)的记录 -->
        ) a
        WHERE a.rn = 1
    </select>

    <select id="getByScheduleId" resultType="com.demo.entity.Attendance">
        SELECT * FROM attendance WHERE schedule_id = #{scheduleId} LIMIT 1
    </select>

    <!-- 按地区和日期查询考勤统计数据 - 优化版本 -->
    <select id="getAttendanceStatsByArea" resultType="java.util.Map">
        SELECT
            <choose>
                <when test="groupByLevel == 'county'">
                    u.county as area_name,
                    u.county,
                    u.city
                </when>
                <when test="groupByLevel == 'township'">
                    u.township as area_name,
                    u.township,
                    u.county,
                    u.city
                </when>
                <when test="groupByLevel == 'hamlet'">
                    u.hamlet as area_name,
                    u.hamlet,
                    u.township,
                    u.county,
                    u.city
                </when>
                <when test="groupByLevel == 'site'">
                    u.site as area_name,
                    u.site,
                    u.hamlet,
                    u.township,
                    u.county,
                    u.city
                </when>
                <otherwise>
                    u.city as area_name,
                    u.city
                </otherwise>
            </choose>,
            COUNT(DISTINCT s.user_id) as total_scheduled,
            COUNT(DISTINCT CASE WHEN a.check_in_time IS NOT NULL THEN s.user_id END) as attended_count
        FROM schedule s
        INNER JOIN users u ON s.user_id = u.user_id AND u.state = 0
        INNER JOIN shift sh ON s.shift_id = sh.id
        LEFT JOIN attendance a ON s.id = a.schedule_id
        WHERE DATE(s.schedule_date) = #{date}
        <if test="city != null and city != ''">
            AND u.city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
        GROUP BY
        <choose>
            <when test="groupByLevel == 'county'">
                u.county, u.city
            </when>
            <when test="groupByLevel == 'township'">
                u.township, u.county, u.city
            </when>
            <when test="groupByLevel == 'hamlet'">
                u.hamlet, u.township, u.county, u.city
            </when>
            <when test="groupByLevel == 'site'">
                u.site, u.hamlet, u.township, u.county, u.city
            </when>
            <otherwise>
                u.city
            </otherwise>
        </choose>
        ORDER BY area_name
    </select>

    <!-- 批量查询多日期考勤统计数据 - 用于月度/年度汇总优化 -->
    <select id="getAttendanceStatsByAreaBatch" resultType="java.util.Map">
        SELECT
            s.schedule_date,
            <choose>
                <when test="groupByLevel == 'county'">
                    u.county as area_name,
                    u.county,
                    u.city
                </when>
                <when test="groupByLevel == 'township'">
                    u.township as area_name,
                    u.township,
                    u.county,
                    u.city
                </when>
                <when test="groupByLevel == 'hamlet'">
                    u.hamlet as area_name,
                    u.hamlet,
                    u.township,
                    u.county,
                    u.city
                </when>
                <when test="groupByLevel == 'site'">
                    u.site as area_name,
                    u.site,
                    u.hamlet,
                    u.township,
                    u.county,
                    u.city
                </when>
                <otherwise>
                    u.city as area_name,
                    u.city
                </otherwise>
            </choose>,
            COUNT(DISTINCT s.user_id) as total_scheduled,
            COUNT(DISTINCT CASE WHEN a.check_in_time IS NOT NULL THEN s.user_id END) as attended_count
        FROM schedule s
        INNER JOIN users u ON s.user_id = u.user_id AND u.state = 0
        INNER JOIN shift sh ON s.shift_id = sh.id
        LEFT JOIN attendance a ON s.id = a.schedule_id
        WHERE DATE(s.schedule_date) BETWEEN #{startDate} AND #{endDate}
        <if test="city != null and city != ''">
            AND u.city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
        GROUP BY
            s.schedule_date,
        <choose>
            <when test="groupByLevel == 'county'">
                u.county, u.city
            </when>
            <when test="groupByLevel == 'township'">
                u.township, u.county, u.city
            </when>
            <when test="groupByLevel == 'hamlet'">
                u.hamlet, u.township, u.county, u.city
            </when>
            <when test="groupByLevel == 'site'">
                u.site, u.hamlet, u.township, u.county, u.city
            </when>
            <otherwise>
                u.city
            </otherwise>
        </choose>
        ORDER BY s.schedule_date, area_name
    </select>
</mapper>