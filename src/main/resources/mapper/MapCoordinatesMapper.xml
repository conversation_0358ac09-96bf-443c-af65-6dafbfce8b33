<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.MapCoordinatesMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.MapCoordinates">
    <!--@mbg.generated-->
    <!--@Table map_coordinates-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, city, county, longitude, latitude, creation_time
  </sql>
</mapper>