<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.SysOperLogMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.SysOperLog">
    <!--@mbg.generated-->
    <!--@Table `sys_oper_log`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="request_method" jdbcType="VARCHAR" property="requestMethod" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="oper_name" jdbcType="VARCHAR" property="operName" />
    <result column="oper_url" jdbcType="VARCHAR" property="operUrl" />
    <result column="oper_ip" jdbcType="VARCHAR" property="operIp" />
    <result column="oper_param" jdbcType="VARCHAR" property="operParam" />
<!--    <result column="json_result" jdbcType="VARCHAR" property="jsonResult" />-->
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
    <result column="execute_time" jdbcType="BIGINT" property="executeTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `title`, `business_type`, `method`, `request_method`, `user_id`, `oper_name`, 
    `oper_url`, `oper_ip`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, 
    `execute_time`
  </sql>
</mapper>