<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--suppress ALL -->
<mapper namespace="com.demo.mapper.UsersMapper">
    <resultMap id="BaseResultMap" type="com.demo.entity.Users">
        <id column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="county" jdbcType="VARCHAR" property="county"/>
        <result column="township" jdbcType="VARCHAR" property="township"/>
        <result column="hamlet" jdbcType="VARCHAR" property="hamlet"/>
        <result column="site" jdbcType="VARCHAR" property="site"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <collection property="roles" ofType="com.demo.entity.Role">
            <id property="roleId" column="role_id"/>
            <result property="roleCode" column="role_code"/>
            <result property="roleName" column="role_name"/>
        </collection>
    </resultMap>

    <select id="selectPermissionList" resultType="java.lang.String">
        SELECT permission_code
        FROM `users`
                 LEFT JOIN role_users ru ON ru.user_id = users.user_id
                 LEFT JOIN role_permission rp ON ru.role_id = rp.role_id
                 LEFT JOIN permission ON rp.permission_id = permission.permission_id
        WHERE users.user_id = #{id}
        GROUP BY permission_code
    </select>

    <select id="selectroleList" resultType="java.lang.String">
        SELECT role_code
        FROM `users`
                 LEFT JOIN role_users ru ON ru.user_id = users.user_id
                 LEFT JOIN role ON ru.role_id = role.role_id
        WHERE users.user_id = #{id}
        GROUP BY role_code
    </select>

    <update id="updateBatchByIds">
        UPDATE users
        SET state = 2 WHERE user_id IN
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updatefrozenUserList">
        UPDATE users
        SET state = 1 WHERE user_id IN
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectUsers" resultMap="BaseResultMap">
        select
        user.user_id as user_id,
        user.city as city,
        user.county as county,
        user.township as township,
        user.hamlet as hamlet,
        user.site as site,
        user.state as state,
        user.phone as phone,
        user.name as name,
        user.sex as sex,
        user.dept_name as dept_name,
        user.create_time as create_time,
        r.role_id ,
        r.role_code,
        r.role_name
        from (
        select
        `user_id`,
        `phone`,
        `name`,
        `sex`,
        `dept_name`,
        `city`,
        `county`,
        `township`,
        `hamlet`,
        `site`,
        `state`,
        `create_time`
        from users
        <where>
            <if test="name != null and name != ''">
                and name like CONCAT('%', #{name}, '%')
            </if>
            <if test="phone != null and phone != ''">
                and phone like CONCAT('%', #{phone}, '%')
            </if>
            <if test="sex != null and sex != ''">
                and sex = #{sex}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="county != null and county != ''">
                and county = #{county}
            </if>
            <if test="township != null and township != ''">
                and township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                and hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                and site = #{site}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
        </where>
        LIMIT #{offset}, #{pageSize}
        ) user
        left join role_users ru on ru.user_id = user.user_id
        left join role r on r.role_id = ru.role_id
    </select>



    <select id="countUsers" resultType="long">
        select count(*)
        from users
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="sex != null and sex != ''">
                and sex = #{sex}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="county != null and county != ''">
                and county = #{county}
            </if>
            <if test="township != null and township != ''">
                and township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                and hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                and site = #{site}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
        </where>
    </select>

    <select id="selectProselytizer" resultMap="BaseResultMap">
        SELECT filtered_users.user_id,
               filtered_users.phone,
               filtered_users.name,
               filtered_users.sex,
               filtered_users.dept_name,
               filtered_users.city,
               filtered_users.county,
               filtered_users.township,
               filtered_users.hamlet,
               filtered_users.site,
               filtered_users.state,
               filtered_users.create_time
        FROM
        (SELECT user_id,
                phone,
                name,
                sex,
                dept_name,
                city,
                county,
                township,
                hamlet,
                site,
                state,
                create_time
         FROM users
        WHERE state = 0
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        ) AS filtered_users
            LEFT JOIN
        role_users ru ON filtered_users.user_id = ru.user_id
            LEFT JOIN
        role r ON ru.role_id = r.role_id
        WHERE r.role_name = '劝导员'
    </select>

    <select id="selectProselytizerList" resultMap="BaseResultMap">
        SELECT filtered_users.user_id,
               filtered_users.phone,
               filtered_users.name,
               filtered_users.sex,
               filtered_users.dept_name,
               filtered_users.city,
               filtered_users.county,
               filtered_users.township,
               filtered_users.hamlet,
               filtered_users.site,
               filtered_users.state,
               filtered_users.create_time
        FROM (SELECT user_id,
                     phone,
                     name,
                     sex,
                     dept_name,
                     city,
                     county,
                     township,
                     hamlet,
                     site,
                     state,
                     create_time
              FROM users
        WHERE state = 0
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        ) AS filtered_users
            LEFT JOIN
        role_users ru ON filtered_users.user_id = ru.user_id
            LEFT JOIN
        role r ON ru.role_id = r.role_id
        WHERE r.role_name = '劝导员'
    </select>

    <select id="selectProselytizerListMap" resultType="java.util.Map">
        WITH filtered_users AS (
        SELECT
        user_id,
        phone,
        name,
        sex,
        dept_name,
        city,
        county,
        township,
        hamlet,
        site,
        state
        FROM
        users
        WHERE
        state = 0
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND site = #{site}
        </if>
        ),
        user_roles AS (
        SELECT
        ru.user_id
        FROM
        role_users ru
        INNER JOIN
        role r ON ru.role_id = r.role_id
        WHERE
        r.role_name = '劝导员'
        )
        SELECT
        fu.user_id,
        fu.phone,
        fu.name,
        fu.sex,
        fu.dept_name,
        fu.city,
        fu.county,
        fu.township,
        fu.hamlet,
        fu.site,
        fu.state,
        COALESCE(SUM(CASE WHEN ap.disposal_status = 1 THEN 1 ELSE 0 END), 0) AS processed_count,
        COALESCE(SUM(CASE WHEN ap.disposal_status = 0 THEN 1 ELSE 0 END), 0) AS unprocessed_count
        FROM
        filtered_users fu
        INNER JOIN
        user_roles ur ON fu.user_id = ur.user_id
        LEFT JOIN
        accurate_persuasion ap ON fu.user_id = ap.user_id
        GROUP BY
        fu.user_id,
        fu.phone,
        fu.name,
        fu.sex,
        fu.dept_name,
        fu.city,
        fu.county,
        fu.township,
        fu.hamlet,
        fu.site
    </select>

    <select id="getAllThePersuaders" resultType="com.demo.entity.VO.AllThePersuadersVO">
        select distinct user.user_id,
                        user.phone,
                        user.name,
                        user.sex,
                        user.dept_name,
                        user.city,
                        user.county,
                        user.township,
                        user.hamlet,
                        user.site,
                        user.state,
                        usg.group_id,
                        sg.group_name,
                        usg.start_date,
                        usg.end_date,
                        sg.work_pattern
        from (select *
              from users
        where state = 0
        <if test="iocationDTO.name != null and iocationDTO.name != ''">
            AND name LIKE CONCAT('%', #{iocationDTO.name}, '%')
        </if>
        <if test="iocationDTO.city != null and iocationDTO.city != ''">
            AND city = #{iocationDTO.city}
        </if>
        <if test="iocationDTO.county != null and iocationDTO.county != ''">
            AND county = #{iocationDTO.county}
        </if>
        <if test="iocationDTO.township != null and iocationDTO.township != ''">
            AND township = #{iocationDTO.township}
        </if>
        <if test="iocationDTO.hamlet != null and iocationDTO.hamlet != ''">
            AND hamlet = #{iocationDTO.hamlet}
        </if>
        <if test="iocationDTO.site != null and iocationDTO.site != ''">
            AND site = #{iocationDTO.site}
        </if>) user
                 left join role_users ru on user.user_id = ru.user_id
                 left join role r on ru.role_id = r.role_id
                 left join user_shift_group usg on usg.user_id = user.user_id
                 left join shift_group sg on usg.group_id = sg.id
    where r.role_name = '劝导员'
    </select>

    <select id="getAttendanceUser" resultType="java.util.Map">
        WITH persuaders AS (
            -- 先查询指定地点的劝导员
            SELECT DISTINCT 
                u.user_id,
                u.name,
                u.phone,
                u.city,
                u.county,
                u.township,
                u.hamlet,
                u.site,
                u.dept_name
            FROM users u
            INNER JOIN role_users ru ON u.user_id = ru.user_id
            INNER JOIN role r ON ru.role_id = r.role_id
            WHERE u.state = 0 
            AND r.role_name = '劝导员'
            <if test="city != null and city != ''">
                AND u.city = #{city}
                <if test="county != null and county != ''">
                    AND u.county = #{county}
                    <if test="township != null and township != ''">
                        AND u.township = #{township}
                        <if test="hamlet != null and hamlet != ''">
                            AND u.hamlet = #{hamlet}
                            <if test="site != null and site != ''">
                                AND u.site = #{site}
                            </if>
                        </if>
                    </if>
                </if>
            </if>
        )
        SELECT 
            p.*,
            s.id as schedule_id,
            DATE_FORMAT(s.schedule_date, '%Y-%m-%d %H:%i:%s') as schedule_date,
            sh.shift_name,
            TIME_FORMAT(sh.start_time, '%H:%i') as start_time,
            TIME_FORMAT(sh.end_time, '%H:%i') as end_time,
            CASE 
                -- 如果有考勤记录，根据考勤状态显示
                WHEN a.status IS NOT NULL THEN
                    CASE
        WHEN a.status IN (0, 5, 6, 7) THEN '正常'
        WHEN a.status = 1 THEN '迟到'
        WHEN a.status = 2 THEN '早退'
        WHEN a.status = 3 THEN '缺勤'
        WHEN a.status = 4 THEN '请假'
                    END
                -- 如果查询日期是今天，则根据当前时间判断
                WHEN DATE(#{date}) = CURDATE() THEN
                    CASE
                        WHEN CURTIME() &lt; sh.start_time THEN '休息'
                        WHEN CURTIME() > sh.end_time THEN '缺勤'
                        ELSE '未打卡'
                    END
                -- 如果是过去的日期，没有考勤记录就是缺勤
                WHEN DATE(#{date}) &lt; CURDATE() THEN '缺勤'
                -- 如果是将来的日期，显示待上班
                ELSE '待上班'
            END as status_text,
            CASE 
                -- 如果有考勤记录，使用考勤状态
                WHEN a.status IS NOT NULL THEN a.status
                -- 如果查询日期是今天，则根据当前时间判断
                WHEN DATE(#{date}) = CURDATE() THEN
                    CASE
                        WHEN CURTIME() &lt; sh.start_time THEN -2  -- 待上班
                        WHEN CURTIME() > sh.end_time THEN 3    -- 缺勤
                        ELSE -1                                -- 未打卡
                    END
                -- 如果是过去的日期，没有考勤记录就是缺勤
                WHEN DATE(#{date}) &lt; CURDATE() THEN 3
                -- 如果是将来的日期，显示待上班
                ELSE -2
            END as attendance_status,
            p.dept_name,
            IFNULL(TIME_FORMAT(a.check_in_time, '%H:%i'), '') as check_in_time,
            IFNULL(TIME_FORMAT(a.check_out_time, '%H:%i'), '') as check_out_time
        FROM persuaders p
        LEFT JOIN schedule s ON p.user_id = s.user_id AND DATE(s.schedule_date) = #{date}
        LEFT JOIN shift sh ON s.shift_id = sh.id
        LEFT JOIN attendance a ON s.id = a.schedule_id
        ORDER BY p.name, sh.start_time
    </select>

    <!-- 1. 查询劝导员 -->
    <select id="getPersuaders" resultType="java.util.Map">
        SELECT DISTINCT 
            u.user_id,
            u.name,
            u.phone,
            u.city,
            u.county,
            u.township,
            u.hamlet,
            u.site,
            u.dept_name as  deptName
        FROM users u
        INNER JOIN role_users ru ON u.user_id = ru.user_id
        INNER JOIN role r ON ru.role_id = r.role_id
        WHERE u.state = 0 
        AND r.role_name = '劝导员'
        <if test="city != null and city != ''">
            AND u.city = #{city}
            <if test="county != null and county != ''">
                AND u.county = #{county}
                <if test="township != null and township != ''">
                    AND u.township = #{township}
                    <if test="hamlet != null and hamlet != ''">
                        AND u.hamlet = #{hamlet}
                        <if test="site != null and site != ''">
                            AND u.site = #{site}
                        </if>
                    </if>
                </if>
            </if>
        </if>
    </select>

    <!-- 2. 查询排班记录 -->
    <select id="getSchedules" resultType="java.util.Map">
        SELECT 
            s.*,
            u.name as user_name,
            sh.shift_name,
            sh.start_time,
            sh.end_time
        FROM schedule s
        INNER JOIN users u ON s.user_id = u.user_id
        LEFT JOIN shift sh ON s.shift_id = sh.id
        WHERE s.user_id IN (
            SELECT u2.user_id
            FROM users u2
            INNER JOIN role_users ru ON u2.user_id = ru.user_id
            INNER JOIN role r ON ru.role_id = r.role_id
            WHERE u2.state = 0 
            AND r.role_name = '劝导员'
            <if test="city != null and city != ''">
                AND u2.city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND u2.county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND u2.township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND u2.hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND u2.site = #{site}
            </if>
        ) 
        AND DATE(s.schedule_date) = #{date}
    </select>
</mapper>