<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.DeviceMapper">
    <resultMap id="BaseResultMap" type="com.demo.entity.Device">
        <!--@mbg.generated-->
        <!--@Table `device`-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="equipment_number" jdbcType="VARCHAR" property="equipmentNumber"/>
        <result column="device_type" jdbcType="VARCHAR" property="deviceType"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="stream_key" jdbcType="VARCHAR" property="streamKey"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="county" jdbcType="VARCHAR" property="county"/>
        <result column="township" jdbcType="VARCHAR" property="township"/>
        <result column="hamlet" jdbcType="VARCHAR" property="hamlet"/>
        <result column="site" jdbcType="VARCHAR" property="site"/>
        <result column="scale" jdbcType="DOUBLE" property="scale"/>
        <result column="maintainer_phone" jdbcType="VARCHAR" property="maintainerPhone"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="last_online_time" jdbcType="TIMESTAMP" property="lastOnlineTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="dev_mode" jdbcType="BOOLEAN" property="devMode"/>
        <result column="address_text" jdbcType="VARCHAR" property="addressText"/>
        <result column="camera_url1" jdbcType="VARCHAR" property="cameraUrl1"/>
        <result column="camera_url2" jdbcType="VARCHAR" property="cameraUrl2"/>
        <result column="clip_rect1" jdbcType="VARCHAR" property="clipRect1"
                typeHandler="com.demo.handler.IntegerArrayJSONTypeHandler"/>
        <result column="clip_rect2" jdbcType="VARCHAR" property="clipRect2"
                typeHandler="com.demo.handler.IntegerArrayJSONTypeHandler"/>
        <result column="line1" jdbcType="VARCHAR" property="line1"
                typeHandler="com.demo.handler.IntegerArrayJSONTypeHandler"/>
        <result column="line2" jdbcType="VARCHAR" property="line2"
                typeHandler="com.demo.handler.IntegerArrayJSONTypeHandler"/>
        <result column="nohelmet_threshold" jdbcType="FLOAT" property="nohelmetThreshold"/>
        <result column="weather_shield_threshold" jdbcType="FLOAT" property="weatherShieldThreshold"/>
        <result column="persuad_time_duration" jdbcType="INTEGER" property="persuadTimeDuration"/>
        <result column="advicer_face_recog_duration" jdbcType="INTEGER" property="advicerFaceRecogDuration"/>
        <result column="face_recog_distance_threshold" jdbcType="FLOAT" property="faceRecogDistanceThreshold"/>
        <result column="load_param_interval" jdbcType="INTEGER" property="loadParamInterval"/>
        <result column="load_face_database_interval" jdbcType="INTEGER" property="loadFaceDatabaseInterval"/>
        <result column="bike_trackid_life" jdbcType="INTEGER" property="bikeTrackidLife"/>
        <result column="save_violate_video" jdbcType="BOOLEAN" property="saveViolateVideo"/>
        <result column="start_end_time" jdbcType="INTEGER" property="startEndTime"/>
        <result column="video_fps" jdbcType="INTEGER" property="videoFps"/>
        <result column="announce_times" jdbcType="LONGVARCHAR" property="announceTimes"/>
        <result column="max_announce_times" jdbcType="INTEGER" property="maxAnnounceTimes"/>
        <result column="announce_timeout" jdbcType="INTEGER" property="announceTimeout"/>
        <result column="announce_volume" jdbcType="LONGVARCHAR" property="announceVolume"/>
        <result column="unmute_start" jdbcType="INTEGER" property="unmuteStart"/>
        <result column="unmute_end" jdbcType="INTEGER" property="unmuteEnd"/>
        <result column="bike_plates_quantity" jdbcType="INTEGER" property="bikePlatesQuantity"/>
        <result column="retry_intervals" jdbcType="VARCHAR" property="retryIntervals"
                typeHandler="com.demo.handler.IntegerArrayJSONTypeHandler"/>
        <result column="online_status_interval" jdbcType="INTEGER" property="onlineStatusInterval"/>
        <result column="backward_priority" jdbcType="BOOLEAN" property="backwardPriority"/>
        <result column="model_file" jdbcType="LONGVARCHAR" property="modelFile"/>
        <result column="urgent" jdbcType="BOOLEAN" property="urgent"/>
    </resultMap>

    <select id="selectDeviceOnline" resultType="java.lang.Double">
        SELECT ROUND(IFNULL(((COUNT(CASE WHEN state = 0 THEN 1 ELSE NULL END) + 0.0) / (COUNT(*) + 0.0)) * 100, 0),
                     2) AS online_rate
        FROM device;
    </select>
</mapper>