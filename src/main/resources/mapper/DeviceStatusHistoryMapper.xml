<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.DeviceStatusHistoryMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.DeviceStatusHistory">
    <!--@mbg.generated-->
    <!--@Table device_status_history-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="equipment_number" jdbcType="VARCHAR" property="equipmentNumber" />
    <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="duration" jdbcType="BIGINT" property="duration" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, equipment_number, device_type, start_time, end_time, `state`, reason, duration,`ip`
  </sql>
</mapper>