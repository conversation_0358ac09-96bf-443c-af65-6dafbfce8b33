<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.OvertimeMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.Overtime">
    <!--@mbg.generated-->
    <!--@Table overtime-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="township" jdbcType="VARCHAR" property="township" />
    <result column="hamlet" jdbcType="VARCHAR" property="hamlet" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="equipment_number" jdbcType="VARCHAR" property="equipmentNumber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, user_name, start_time, end_time, duration, city, county, township, hamlet, 
    site, equipment_number, create_time, update_time
  </sql>

  <select id="getUserOvertimeByDate" resultMap="BaseResultMap">
    SELECT * FROM overtime WHERE user_id = #{userId} AND DATE(start_time) = #{date} ORDER BY start_time DESC
    </select>

  <select id="getUserOvertimeByTimeRange" resultMap="BaseResultMap">
    SELECT * FROM overtime WHERE user_id = #{userId} AND start_time >= #{startTime} AND start_time &lt; #{endTime} ORDER BY start_time DESC
  </select>

  <select id="getAllOvertimesByDateRange" resultMap="BaseResultMap">
    SELECT * FROM overtime WHERE DATE(start_time) >= #{startDate} AND DATE(start_time) &lt;= #{endDate} ORDER BY start_time DESC
  </select>

  <select id="getOvertimesByAreaAndDateRange" resultMap="BaseResultMap">
    SELECT * FROM overtime WHERE DATE(start_time) >= #{startDate} AND DATE(start_time) &lt;= #{endDate}
    <if test="city != null and city != ''">
      AND city = #{city}
    </if>
    <if test="county != null and county != ''">
      AND county = #{county}
    </if>
    <if test="township != null and township != ''">
      AND township = #{township}
    </if>
    <if test="hamlet != null and hamlet != ''">
      AND hamlet = #{hamlet}
    </if>
    <if test="site != null and site != ''">
      AND site = #{site}
    </if>
    ORDER BY start_time DESC
  </select>
</mapper>