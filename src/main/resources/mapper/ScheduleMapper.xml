<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.ScheduleMapper">

    <!-- 查询指定日期的所有排班 -->
    <select id="selectSchedulesByDate" resultType="com.demo.entity.Schedule">
        SELECT * FROM schedule
        WHERE DATE(schedule_date) = #{date}
    </select>

    <!-- 查询指定日期和点位的排班信息 -->
    <select id="selectSchedulesByDateAndLocation" resultType="com.demo.entity.Schedule">
        SELECT s.* 
        FROM schedule s
        INNER JOIN users u ON s.user_id = u.user_id
        WHERE DATE(s.schedule_date) = #{date}
        <if test="city != null and city != ''">
            AND u.city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
    </select>

    <select id="getBasicSchedulingInformation" resultType="com.demo.entity.Schedule">
        select *
        from (SELECT *
              FROM schedule
              WHERE user_id = #{userId}
                AND schedule_date BETWEEN #{startDate} AND #{endDate}) sc
                 left join shift sh on sh.id = sc.shift_id
        order by
        DATE(sc.schedule_date),start_time
    </select>

    <select id="getEmployeeScheduleByUserName" resultType="com.demo.entity.Schedule">
        select *
        from (SELECT *
        FROM schedule
        WHERE schedule_date BETWEEN #{startDate} AND #{endDate}
        <if test="userName != null and userName != ''">
            AND user_name = #{userName}
        </if>
        ) sc
        left join shift sh on sh.id = sc.shift_id
        order by
        DATE(sc.schedule_date),start_time

    </select>

    <!-- 获取指定地点应到人数 -->
    <select id="getScheduledStaffCount" resultType="int">
        SELECT COUNT(DISTINCT user_id)
        FROM (
            SELECT s.user_id
            FROM schedule s
            INNER JOIN users u ON s.user_id = u.user_id
            WHERE DATE(s.schedule_date) = #{date}
            AND u.city = #{city}
            <if test="county != null and county != ''">
                AND u.county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND u.township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND u.hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND u.site = #{site}
            </if>
            GROUP BY s.user_id
        ) t
    </select>

    <!-- 获取指定地点实到人数 -->
    <select id="getPresentStaffCount" resultType="int">
        SELECT COUNT(DISTINCT s.user_id)
        FROM schedule s
        INNER JOIN users u ON s.user_id = u.user_id
        LEFT JOIN attendance a ON s.id = a.schedule_id
        WHERE DATE(s.schedule_date) = #{date}
        AND a.check_in_time IS NOT NULL
        AND u.city = #{city}
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
    </select>

    <!-- 获取指定地点在岗人数 -->
    <select id="getOnDutyStaffCount" resultType="int">
        SELECT COUNT(DISTINCT s.user_id)
        FROM schedule s
        INNER JOIN users u ON s.user_id = u.user_id
        LEFT JOIN attendance a ON s.id = a.schedule_id
        WHERE DATE(s.schedule_date) = #{date}
        AND a.check_in_time IS NOT NULL
        AND a.check_out_time IS NULL
        AND u.city = #{city}
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
    </select>

    <select id="selectByDate" resultType="com.demo.entity.Schedule">
        SELECT * FROM schedule 
        WHERE schedule_date = #{date}
        AND status = 1
        ORDER BY user_id ASC
    </select>

    <!-- 获取指定日期的排班记录 -->
    <select id="getScheduleByDate" resultType="com.demo.entity.Schedule">
        SELECT s.*, u.name as user_name
        FROM schedule s
        LEFT JOIN users u ON s.user_id = u.user_id
        WHERE DATE(s.schedule_date) = #{date}
        ORDER BY s.user_id ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 获取指定日期的排班记录总数 -->
    <select id="getScheduleCountByDate" resultType="int">
        SELECT COUNT(*)
        FROM schedule
        WHERE DATE(schedule_date) = #{date}
    </select>

    <select id="getSchedulesByUserIdAndTimeRange" resultType="com.demo.entity.Schedule">
        SELECT s.* FROM schedule s
        JOIN shift sh ON s.shift_id = sh.id
        WHERE s.user_id = #{userId} 
        AND (
            (CONCAT(DATE(s.schedule_date), ' ', sh.start_time) BETWEEN #{startTime} AND #{endTime})
            OR (CONCAT(DATE(s.schedule_date), ' ', sh.end_time) BETWEEN #{startTime} AND #{endTime})
            OR (CONCAT(DATE(s.schedule_date), ' ', sh.start_time) &lt;= #{startTime} AND CONCAT(DATE(s.schedule_date), ' ', sh.end_time) &gt;= #{endTime})
        )
    </select>

    <select id="queryWorkingHours" resultType="java.util.Map">
        WITH RECURSIVE time_points AS (
            -- 获取所有不重复的时间点
            SELECT DISTINCT
                DATE(s.schedule_date) as work_date,
                TIME(sh.start_time) as start_time,
                TIME(sh.end_time) as end_time,
                COUNT(DISTINCT s.user_id) as staff_count,
                u.city,u.county,u.township,u.hamlet,u.site
            FROM schedule s
            INNER JOIN users u ON s.user_id = u.user_id
            INNER JOIN shift sh ON s.shift_id = sh.id
            WHERE DATE(s.schedule_date) BETWEEN #{startDate} AND #{endDate}
            AND u.city = #{city}
            <if test="county != null and county != ''">
                AND u.county = #{county}
            </if>
            <if test="township != null and township != ''">
                AND u.township = #{township}
            </if>
            <if test="hamlet != null and hamlet != ''">
                AND u.hamlet = #{hamlet}
            </if>
            <if test="site != null and site != ''">
                AND u.site = #{site}
            </if>
            GROUP BY
           u.county, u.township, u.hamlet, u.site, DATE(s.schedule_date), TIME(sh.start_time), TIME(sh.end_time)
        )
        SELECT
            work_date as schedule_date,
            start_time,
            end_time,
            staff_count,city,county,township,hamlet,site
        FROM time_points
        ORDER BY work_date, start_time
    </select>
    
    <!-- 根据地区和日期范围查询排班信息 -->
    <select id="getSchedulesByAreaAndDate" resultType="com.demo.entity.Schedule">
        SELECT s.*, u.name as user_name, 
               u.city, u.county, u.township, u.hamlet, u.site
        FROM schedule s
        INNER JOIN users u ON s.user_id = u.user_id
        WHERE DATE(s.schedule_date) BETWEEN #{startDate} AND #{endDate}
        <if test="city != null and city != ''">
            AND u.city = #{city}
        </if>
        <if test="county != null and county != ''">
            AND u.county = #{county}
        </if>
        <if test="township != null and township != ''">
            AND u.township = #{township}
        </if>
        <if test="hamlet != null and hamlet != ''">
            AND u.hamlet = #{hamlet}
        </if>
        <if test="site != null and site != ''">
            AND u.site = #{site}
        </if>
        ORDER BY u.user_id, s.schedule_date
    </select>

    <!-- 检查排班是否已存在 -->
    <select id="countExistingSchedule" resultType="int">
        SELECT COUNT(*)
        FROM schedule
        WHERE user_id = #{userId}
          AND DATE(schedule_date) = DATE(#{scheduleDate})
          AND shift_id = #{shiftId}
    </select>

    <!-- 批量检查排班是否存在 -->
    <select id="findExistingSchedules" resultType="com.demo.entity.Schedule">
        SELECT s.*
        FROM schedule s
        WHERE (s.user_id, DATE(s.schedule_date), s.shift_id) IN (
            <foreach collection="schedules" item="schedule" separator=",">
                (#{schedule.userId}, DATE(#{schedule.scheduleDate}), #{schedule.shiftId})
            </foreach>
        )
    </select>
</mapper>