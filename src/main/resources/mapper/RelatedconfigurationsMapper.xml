<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.RelatedconfigurationsMapper">
  <resultMap id="BaseResultMap" type="com.demo.entity.Relatedconfigurations">
    <!--@mbg.generated-->
    <!--@Table `relatedconfigurations`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="overload" jdbcType="INTEGER" property="overload" />
    <result column="log_save_time" jdbcType="INTEGER" property="logSaveTime" />
    <result column="off_duty_time" jdbcType="INTEGER" property="offDutyTime" />
    <result column="equipment_offline_time" jdbcType="INTEGER" property="equipmentOfflineTime" />
    <result column="phone" jdbcType="BOOLEAN" property="phone" />
    <result column="effective_persuasion" jdbcType="INTEGER" property="effectivePersuasion" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `overload`, `log_save_time`, `off_duty_time`, `equipment_offline_time`, `phone`, 
    `effective_persuasion`
  </sql>
</mapper>