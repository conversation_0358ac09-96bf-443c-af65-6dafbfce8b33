<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.RoleMapper">
    <resultMap id="BaseResultMap" type="com.demo.entity.Role">
        <!--@mbg.generated-->
        <!--@Table `role`-->
        <id column="role_id" jdbcType="INTEGER" property="roleId"/>
        <result column="role_code" jdbcType="VARCHAR" property="roleCode"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <collection property="permissionList" ofType="com.demo.entity.Permission">
            <id property="permissionId" column="permission_id"/>
            <result property="permissionCode" column="permission_code"/>
            <result property="remarks" column="remarks"/>
            <result property="plate" column="plate"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        role_id,
        role_code,
        role_name
    </sql>

    <select id="selectRolesWithPermissions" resultMap="BaseResultMap">
        SELECT r.role_id,
        r.role_code,
        r.role_name,
        p.permission_id,
        p.permission_code,
        p.remarks,
        p.plate
        FROM (
        SELECT role_id,
        role_code,
        role_name
        FROM role
        <where>
            <if test="roleCode != null and roleCode != ''">
                AND role_code LIKE CONCAT('%', #{roleCode}, '%')
            </if>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
        </where>
        LIMIT #{offset}, #{pageSize}
        ) r
        LEFT JOIN role_permission rp ON r.role_id = rp.role_id
        LEFT JOIN permission p ON rp.permission_id = p.permission_id
    </select>

    <select id="countRoles" resultType="long">
        SELECT COUNT(*)
        FROM role
        <where>
            <if test="roleCode != null and roleCode != ''">
                AND role_code LIKE CONCAT('%', #{roleCode}, '%')
            </if>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
        </where>
    </select>
</mapper>