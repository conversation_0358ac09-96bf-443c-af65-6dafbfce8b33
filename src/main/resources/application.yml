spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  profiles:
    active: prod
#    active: dev

# 使用 mybatis-plus 的配置
#mybatis-plus:
#  type-aliases-package: com.demo.entity
#  configuration:
#    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  global-config:
#    db-config:
#      id-type: auto
#      logic-delete-field: deleted
#      logic-delete-value: 1
#      logic-not-delete-value: 0
