# 农村两轮车监控系统 - SRS稳定推流优化配置
# 专门解决RTSP推流时间戳问题和推流中断问题

listen              1935;
max_connections     1000;
daemon              on;

# HTTP API配置
http_api {
    enabled         on;
    listen          1985;
}

# HTTP服务器配置（用于HLS播放）
http_server {
    enabled         on;
    listen          8088;
    dir             /home/<USER>
}

# WebRTC配置
rtc_server {
    enabled on;
    listen 8000; # UDP port
    candidate ***************; # 使用您的公网IP地址替换
}

# 默认虚拟主机配置
vhost __defaultVhost__ {
    # HLS配置 - 优化为更短分片提高稳定性
    hls {
        enabled         on;
        hls_fragment    3;      # 分片时长3秒（稍微增加以提高稳定性）
        hls_window      60;     # 窗口时长1分钟（减少内存占用）
        hls_on_error    continue; # 出错时继续录制
    }
    
    # HTTP-FLV配置 - 优化缓存策略
    http_remux {
        enabled     on;
        fast_cache 3;           # 快速缓存3秒（减少以降低延迟）
        drop_if_not_match off;  # 不匹配时不丢弃（提高兼容性）
        has_audio on;  
        has_video on;
        guess_has_av on;
        mount       [vhost]/[app]/[stream].flv;
    }
    
    # WebRTC配置
    rtc {
        enabled     on;
        rtmp_to_rtc on;         # RTMP转WebRTC
        rtc_to_rtmp on;         # WebRTC转RTMP
    }

    # 播放配置 - 优化缓存和队列
    play{
        gop_cache       on;     # 开启GOP缓存提高播放稳定性
        queue_length 30;        # 增加队列长度处理网络抖动
        gop_cache_max_frames 1000; # 减少GOP缓存帧数
        mw_latency      100;    # 设置最小等待延迟100ms
        send_min_interval 10.0; # 发送最小间隔10ms
    }
    
    # 推流配置 - 关键优化
    publish {
        mr off;                 # 关闭merge read
        parse_sps off;          # 关闭SPS解析（避免某些摄像头兼容问题）
        firstpkt_timeout 5000;  # 首包超时5秒
        normal_timeout 3000;    # 正常超时3秒
    }
	
	# DVR录制配置 - 优化为短时间分段
	dvr {
		# 启用DVR录制
		enabled		on;
		
		# 录制所有流
		dvr_apply		all;  
		
		# 分段录制模式，短时间分段提高稳定性
		dvr_plan        segment;
		
		# 录制文件保存路径，按日期和流名称分目录
		dvr_path        /home/<USER>/[app]/[stream]/[2006]/[01]/[02]/[15].[04].[05].[999].mp4;
		
		# 每个录制文件的时长（秒），5分钟 = 300秒（短分段测试）
		dvr_duration    300;
		
		# 等待关键帧再切分文件，确保播放质量
		dvr_wait_keyframe   on;
		
		# 时间戳矫正算法 - 关键修改
		time_jitter     zero;   # 改为zero模式，不进行时间戳矫正
		}
	
    # 全局时间戳处理配置 - 新增
    time_jitter {
        # 时间戳抖动处理策略
        # zero: 不处理时间戳，保持原始时间戳
        # full: 完全重新生成时间戳（可能导致某些流中断）
        # careful: 谨慎处理，只在必要时调整
        algorithm zero;
    }
    
    # 流处理优化配置 - 新增
    forward {
        # 转发失败时的处理策略
        enabled off;
    }
    
    # 安全配置
    security {
        # 允许所有IP推流（生产环境建议限制）
        allow_publish all;
        allow_play all;
    }
	
    # HTTP回调配置 - 核心部分
    http_hooks {
        enabled on;
        
        # 推流权限控制回调
        on_publish http://***************:8081/api/on_publish;
        
        # 推流结束回调
        on_unpublish http://***************:8081/api/on_unpublish;
        
        # 观看开始回调
        on_play http://***************:8081/api/on_play;
        
        # 观看结束回调
        on_stop http://***************:8081/api/on_stop;
		
		# 添加DVR录制回调
		on_dvr http://***************:8081/api/on_dvr;
    }
    
    # 错误处理配置 - 新增
    refer {
        # 允许所有来源
        enabled off;
    }
    
    # 带宽控制 - 新增
    bandcheck {
        enabled off;
    }
}
