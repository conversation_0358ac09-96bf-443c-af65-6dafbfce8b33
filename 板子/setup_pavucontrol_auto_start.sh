#!/bin/bash

# 定义变量
USER_HOME=$HOME  # 使用 $HOME 环境变量自动获取当前用户的主目录
SCRIPT_DIR="$USER_HOME/srs"
SCRIPT_PATH="$SCRIPT_DIR/start_pavucontrol.sh"
DESKTOP_FILE_PATH="$USER_HOME/.config/autostart/start_pavucontrol.desktop"

# 创建脚本目录（如果不存在）
mkdir -p "$SCRIPT_DIR"

# 创建 start_pavucontrol.sh 脚本
cat <<EOL > "$SCRIPT_PATH"
#!/bin/bash

# 设置DISPLAY环境变量（通常为:0）
export DISPLAY=:0

# 设置XAUTHORITY环境变量（如果需要）
export XAUTHORITY=/home/<USER>/.Xauthority

# 启动 pavucontrol
/usr/bin/pavucontrol &
EOL

# 给脚本添加可执行权限
chmod +x "$SCRIPT_PATH"

# 创建 .desktop 文件以自动启动脚本
mkdir -p "$(dirname "$DESKTOP_FILE_PATH")"

# 创建 .desktop 文件以自动启动脚本
cat <<EOL > "$DESKTOP_FILE_PATH"
[Desktop Entry]
Type=Application
Exec=$SCRIPT_PATH
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
Name=Start Pavucontrol
Comment=Automatically starts pavucontrol on login
EOL

echo "脚本和.desktop文件已成功创建并设置。请手动运行$start_pavucontrol.sh测试或重启系统确认自动启动功能。"